import React, { useState } from 'react';
import { usePaymentRequestPDF, PaymentRequestData } from './PaymentRequestForm';
import { Button } from './button';
import { FileText, Download, Eye } from 'lucide-react';

interface PaymentRequestFormControllerProps {
  data?: PaymentRequestData;
  onDataChange?: (data: PaymentRequestData) => void;
  showForm?: boolean;
}

export const PaymentRequestFormController: React.FC<PaymentRequestFormControllerProps> = ({
  data,
  onDataChange,
  showForm = true,
}) => {
  const { downloadPDF, openPDFInNewWindow } = usePaymentRequestPDF();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<PaymentRequestData>(data || {});

  const handleInputChange = (field: string, value: string) => {
    const updatedData = { ...formData };
    
    // Handle nested object properties
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      const parentData = updatedData[parent as keyof PaymentRequestData] as Record<string, any> || {};
      updatedData[parent as keyof PaymentRequestData] = {
        ...parentData,
        [child]: value,
      } as any;
    } else {
      (updatedData as any)[field] = value;
    }
    
    setFormData(updatedData);
    onDataChange?.(updatedData);
  };

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      await downloadPDF(formData, `payment-request-form-${new Date().toISOString().split('T')[0]}.pdf`);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      // You might want to show a notification here
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreview = async () => {
    setIsLoading(true);
    try {
      await openPDFInNewWindow(formData);
    } catch (error) {
      console.error('Error opening PDF preview:', error);
      // You might want to show a notification here
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <FileText className="h-6 w-6" />
          <h2 className="text-xl font-semibold">Payment Request Form</h2>
        </div>
        <div className="flex space-x-2">
          <Button 
            onClick={handlePreview} 
            disabled={isLoading}
            variant="secondary"
            size="sm"
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button 
            onClick={handleDownload} 
            disabled={isLoading}
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
        </div>
      </div>

      {/* Form Fields */}
      {showForm && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            
            <div>
              <label className="block text-sm font-medium mb-1">Date</label>
              <input
                type="text"
                value={formData.date || ''}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="22 Jul 2025"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Total Amount</label>
              <input
                type="text"
                value={formData.amount || ''}
                onChange={(e) => handleInputChange('amount', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="757,496.52"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Description</label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="With Holding Tax 3% Amount Thb 204.52"
              />
            </div>
          </div>

          {/* Account Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Account Information</h3>
            
            <div>
              <label className="block text-sm font-medium mb-1">Account Name</label>
              <input
                type="text"
                value={formData.fromAccount?.name || ''}
                onChange={(e) => handleInputChange('fromAccount.name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="E-POS Service Company Limited"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Account Type</label>
              <input
                type="text"
                value={formData.fromAccount?.type || ''}
                onChange={(e) => handleInputChange('fromAccount.type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Saving Account"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Bank</label>
              <input
                type="text"
                value={formData.fromAccount?.bank || ''}
                onChange={(e) => handleInputChange('fromAccount.bank', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="BBL"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Branch</label>
              <input
                type="text"
                value={formData.fromAccount?.branch || ''}
                onChange={(e) => handleInputChange('fromAccount.branch', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Head Office"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Account Number</label>
              <input
                type="text"
                value={formData.fromAccount?.accountNo || ''}
                onChange={(e) => handleInputChange('fromAccount.accountNo', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="224-0-33223-5"
              />
            </div>
          </div>

          {/* Transaction Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Transaction Details</h3>
            
            <div>
              <label className="block text-sm font-medium mb-1">Merchant Gross</label>
              <input
                type="text"
                value={formData.transactions?.merchantGross || ''}
                onChange={(e) => handleInputChange('transactions.merchantGross', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="755,987.00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Merchant Net</label>
              <input
                type="text"
                value={formData.transactions?.merchantNet || ''}
                onChange={(e) => handleInputChange('transactions.merchantNet', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="8,880.85"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Discount Earned</label>
              <input
                type="text"
                value={formData.transactions?.discountEarned || ''}
                onChange={(e) => handleInputChange('transactions.discountEarned', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="6,889.10"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">VAT 7%</label>
              <input
                type="text"
                value={formData.transactions?.vat || ''}
                onChange={(e) => handleInputChange('transactions.vat', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="482.23"
              />
            </div>
          </div>

          {/* Summary */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Summary</h3>
            
            <div>
              <label className="block text-sm font-medium mb-1">Transfer Date</label>
              <input
                type="text"
                value={formData.summary?.transferDate || ''}
                onChange={(e) => handleInputChange('summary.transferDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Direct Credit BBL"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Less Discount</label>
              <input
                type="text"
                value={formData.summary?.lessDiscount || ''}
                onChange={(e) => handleInputChange('summary.lessDiscount', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="748,706.84"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Net Amount</label>
              <input
                type="text"
                value={formData.summary?.netAmount || ''}
                onChange={(e) => handleInputChange('summary.netAmount', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="755,987.00"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentRequestFormController;
