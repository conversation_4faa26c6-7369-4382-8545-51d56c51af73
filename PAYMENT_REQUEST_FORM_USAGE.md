# Payment Request Form Component

This document provides usage examples for the Payment Request Form component that generates PDF documents using react-pdf/renderer.

## Components Created

1. **PaymentRequestForm.tsx** - Core PDF component with react-pdf/renderer
2. **PaymentRequestFormController.tsx** - React component with form controls
3. **paymentRequestFormService.ts** - Service for IPC communication
4. **paymentRequestFormHandler.ts** - Main process handler
5. **payment-request-form.screen.tsx** - Demo screen

## Basic Usage

### 1. Using the PDF Component Directly

```typescript
import { usePaymentRequestPDF, PaymentRequestData } from './components/PaymentRequestForm';

const MyComponent = () => {
  const { downloadPDF, openPDFInNewWindow } = usePaymentRequestPDF();

  const handleGeneratePDF = async () => {
    const data: PaymentRequestData = {
      date: '24 Jul 2025',
      amount: '757,496.52',
      fromAccount: {
        name: 'E-POS Service Company Limited',
        type: 'Saving Account',
        bank: 'BBL',
        branch: 'Head Office',
        accountNo: '224-0-33223-5',
      },
      // ... more data
    };

    await downloadPDF(data, 'my-payment-request.pdf');
  };

  return (
    <button onClick={handleGeneratePDF}>
      Generate Payment Request PDF
    </button>
  );
};
```

### 2. Using the Controller Component

```typescript
import PaymentRequestFormController from './components/PaymentRequestFormController';
import { PaymentRequestData } from './components/PaymentRequestForm';

const MyScreen = () => {
  const [formData, setFormData] = useState<PaymentRequestData>({});

  const handleDataChange = (newData: PaymentRequestData) => {
    setFormData(newData);
    // Optionally save to backend or local storage
  };

  return (
    <PaymentRequestFormController
      data={formData}
      onDataChange={handleDataChange}
      showForm={true}
    />
  );
};
```

### 3. Using the Service for IPC Communication

```typescript
import { paymentRequestFormService } from './services/paymentRequestFormService';

const generateAndSavePDF = async (data: PaymentRequestData) => {
  try {
    // Generate PDF buffer
    const pdfBuffer = await paymentRequestFormService.generatePDF(data);
    
    // Save to specific path
    await paymentRequestFormService.savePDF(data, '/path/to/save/payment-request.pdf');
    
    console.log('PDF generated and saved successfully');
  } catch (error) {
    console.error('Error:', error);
  }
};
```

## PaymentRequestData Interface

```typescript
interface PaymentRequestData {
  date?: string;
  amount?: string;
  fromAccount?: {
    name?: string;
    type?: string;
    bank?: string;
    branch?: string;
    accountNo?: string;
  };
  transactions?: {
    merchantGross?: string;
    merchantNet?: string;
    discountEarned?: string;
    vat?: string;
  };
  description?: string;
  dateRange?: {
    from?: string;
    to?: string;
  };
  mediaClearing?: {
    totalTransactions?: string;
    sumTotal?: string;
  };
  summary?: {
    transferDate?: string;
    totalAmount?: string;
    lessDiscount?: string;
    vatAmount?: string;
    netAmount?: string;
  };
}
```

## Features

- ✅ Professional PDF layout designed for payment request forms
- ✅ Customizable form data with real-time preview
- ✅ Export to PDF with custom filename
- ✅ Preview functionality before download
- ✅ Company account and transaction details management
- ✅ Signature sections for approvals
- ✅ Responsive form interface
- ✅ IPC communication with main process
- ✅ Error handling and validation

## Navigation

The Payment Request Form is available in the sidebar under "Process Daily" → "Payment Request" or by navigating to `/payment-request-form`.

## Customization

You can customize the PDF layout by modifying the styles in `PaymentRequestForm.tsx`. The component uses react-pdf/renderer's StyleSheet API, which is similar to React Native styles.

Example style customization:

```typescript
const customStyles = StyleSheet.create({
  page: {
    // Your custom page styles
    backgroundColor: '#f5f5f5',
    padding: 40,
  },
  title: {
    fontSize: 18,
    color: '#2563eb',
    // More custom styles
  },
});
```

## Integration Notes

- The component integrates with the existing Electron IPC system
- Uses the project's authentication and route protection
- Follows the project's styling patterns with Tailwind CSS
- Error handling uses the project's notification system
- Compatible with the existing button and UI components
