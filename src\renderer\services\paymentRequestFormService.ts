import { PaymentRequestData } from '../components/PaymentRequestForm';
import { safeIpcInvoke } from '../utils/electron';

export interface PaymentRequestFormService {
  generatePDF: (data: PaymentRequestData) => Promise<Buffer>;
  savePDF: (data: PaymentRequestData, filePath: string) => Promise<void>;
}

class PaymentRequestFormServiceImpl implements PaymentRequestFormService {
  async generatePDF(data: PaymentRequestData): Promise<Buffer> {
    try {
      // Use IPC to call the main process for PDF generation
      const result = await safeIpcInvoke('generate-payment-request-pdf', data);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to generate PDF');
      }
      
      return result.data;
    } catch (error) {
      console.error('Error generating payment request PDF:', error);
      throw error;
    }
  }

  async savePDF(data: PaymentRequestData, filePath: string): Promise<void> {
    try {
      const result = await safeIpcInvoke('save-payment-request-pdf', {
        data,
        filePath
      });
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to save PDF');
      }
    } catch (error) {
      console.error('Error saving payment request PDF:', error);
      throw error;
    }
  }
}

export const paymentRequestFormService = new PaymentRequestFormServiceImpl();
