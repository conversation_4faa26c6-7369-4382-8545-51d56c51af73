import React, { useState, useRef, useCallback, useEffect } from 'react';

// Simple test component for resizable functionality
const ResizableTableTest: React.FC = () => {
  const [columnWidths, setColumnWidths] = useState({
    name: 150,
    email: 200,
    role: 120,
    status: 100
  });

  const resizingColumn = useRef<string | null>(null);
  const startX = useRef<number>(0);
  const startWidth = useRef<number>(0);

  const handleMouseDown = useCallback((e: React.MouseEvent, columnKey: string) => {
    console.log('Mouse down on column:', columnKey);
    e.preventDefault();
    e.stopPropagation();
    
    resizingColumn.current = columnKey;
    startX.current = e.clientX;
    startWidth.current = columnWidths[columnKey as keyof typeof columnWidths];
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [columnWidths]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!resizingColumn.current) return;
    
    const deltaX = e.clientX - startX.current;
    const newWidth = Math.max(80, startWidth.current + deltaX);
    
    console.log('Resizing:', resizingColumn.current, 'to width:', newWidth);
    
    setColumnWidths(prev => ({
      ...prev,
      [resizingColumn.current!]: newWidth
    }));
  }, []);

  const handleMouseUp = useCallback(() => {
    console.log('Mouse up');
    resizingColumn.current = null;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [handleMouseMove]);

  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  const ResizableHeader: React.FC<{
    columnKey: string;
    width: number;
    children: React.ReactNode;
  }> = ({ columnKey, width, children }) => (
    <th 
      className="relative px-4 py-2 bg-gray-100 border border-gray-300 text-left select-none"
      style={{ width: `${width}px`, minWidth: '80px' }}
    >
      <div className="min-w-0">{children}</div>
      <div
        className="absolute top-0 right-0 w-2 h-full cursor-col-resize bg-blue-200 hover:bg-blue-400 opacity-50 hover:opacity-100 transition-all duration-200"
        onMouseDown={(e) => handleMouseDown(e, columnKey)}
        title="Drag to resize"
      />
    </th>
  );

  return (
    <div className="p-8">
      <h2 className="text-xl font-bold mb-4">Resizable Table Test</h2>
      
      <div className="mb-4">
        <button 
          onClick={() => setColumnWidths({ name: 150, email: 200, role: 120, status: 100 })}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Reset Widths
        </button>
      </div>

      <div className="border border-gray-300 rounded overflow-hidden">
        <table className="w-full" style={{ tableLayout: 'fixed' }}>
          <thead>
            <tr>
              <ResizableHeader columnKey="name" width={columnWidths.name}>
                Name
              </ResizableHeader>
              <ResizableHeader columnKey="email" width={columnWidths.email}>
                Email
              </ResizableHeader>
              <ResizableHeader columnKey="role" width={columnWidths.role}>
                Role
              </ResizableHeader>
              <ResizableHeader columnKey="status" width={columnWidths.status}>
                Status
              </ResizableHeader>
            </tr>
          </thead>
          <tbody>
            <tr className="border-t">
              <td className="px-4 py-2 border-r" style={{ width: `${columnWidths.name}px` }}>
                <div className="truncate">John Doe</div>
              </td>
              <td className="px-4 py-2 border-r" style={{ width: `${columnWidths.email}px` }}>
                <div className="truncate"><EMAIL></div>
              </td>
              <td className="px-4 py-2 border-r" style={{ width: `${columnWidths.role}px` }}>
                <div className="truncate">Admin</div>
              </td>
              <td className="px-4 py-2" style={{ width: `${columnWidths.status}px` }}>
                <div className="truncate">Active</div>
              </td>
            </tr>
            <tr className="border-t">
              <td className="px-4 py-2 border-r" style={{ width: `${columnWidths.name}px` }}>
                <div className="truncate">Jane Smith</div>
              </td>
              <td className="px-4 py-2 border-r" style={{ width: `${columnWidths.email}px` }}>
                <div className="truncate"><EMAIL></div>
              </td>
              <td className="px-4 py-2 border-r" style={{ width: `${columnWidths.role}px` }}>
                <div className="truncate">User</div>
              </td>
              <td className="px-4 py-2" style={{ width: `${columnWidths.status}px` }}>
                <div className="truncate">Inactive</div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="mt-4">
        <h3 className="font-semibold mb-2">Current Column Widths:</h3>
        <pre className="bg-gray-100 p-2 rounded text-sm">
          {JSON.stringify(columnWidths, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default ResizableTableTest;
