import { useState } from 'react';
import { ReportService, ReportSummary } from '../services/reportService';

interface UseTransactionSummaryReportOptions {
  onSuccess?: (data: ReportSummary) => void;
  onError?: (error: string) => void;
}

export const useTransactionSummaryReport = (options?: UseTransactionSummaryReportOptions) => {
  const [reportData, setReportData] = useState<ReportSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Generate report from filters
  const generateReport = async (filters: any) => {
    setIsLoading(true);
    setError('');
    
    try {
      console.log('📊 Generating transaction summary report...');
      const data = await ReportService.generateTransactionSummaryReport(filters);
      setReportData(data);
      options?.onSuccess?.(data);
      console.log('✅ Report generated successfully');
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate report';
      console.error('❌ Error generating report:', err);
      setError(errorMessage);
      options?.onError?.(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Generate report from transaction details using transaction_summary_report_detail table
  const generateReportFromTransaction = async (trxNo: string) => {
    setIsLoading(true);
    setError('');

    try {
      if (!window.electronAPI?.getTransactionSummaryReportByTrxNo) {
        throw new Error("Transaction summary report function not available");
      }

      console.log('📊 Generating transaction summary report for transaction:', trxNo);
      const result = await window.electronAPI.getTransactionSummaryReportByTrxNo(trxNo);
      
      if (result.success && result.data) {
        setReportData(result.data);
        options?.onSuccess?.(result.data);
        console.log('✅ Transaction summary report generated successfully from transaction_summary_report_detail');
        return result.data;
      } else {
        throw new Error(result.error || "Failed to load transaction summary report data");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate report from transaction';
      console.error("Error generating transaction summary report:", err);
      setError(errorMessage);
      options?.onError?.(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Clear report data and errors
  const clearReport = () => {
    setReportData(null);
    setError('');
  };

  // Reset loading state
  const resetLoading = () => {
    setIsLoading(false);
  };

  return {
    reportData,
    isLoading,
    error,
    generateReport,
    generateReportFromTransaction,
    clearReport,
    resetLoading,
    setReportData,
    setError
  };
};

export default useTransactionSummaryReport;
