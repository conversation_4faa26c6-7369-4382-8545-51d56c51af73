import { StyleSheet } from "@react-pdf/renderer";

export const styles = StyleSheet.create({
  // Page layout
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 30,
    fontFamily: "Helvetica",
  },
  
  // Logo and header
  logoContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 15,
  },
  logo: {
    width: 120,
    height: 30,
  },
  header: {
    textAlign: "center",
    marginBottom: 20,
  },
  title: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
  },
  date: {
    fontSize: 12,
    marginBottom: 5,
    textAlign: "right",
  },

  // Approval section
  approvalContainer: {
    display: "flex",
    flexDirection: "column",
    gap: 4,
  },
  fromSection: {
    display: "flex",
    flexDirection: "row",
    fontSize: 11,
  },
  approvalText: {
    fontSize: 11,
    marginBottom: 20,
    lineHeight: 1.4,
  },

  // Amount section
  amountSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 15,
  },
  amountRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  amountLabel: {
    fontSize: 12,
  },
  amountValue: {
    fontSize: 14,
    marginLeft: 10,
    fontWeight: "bold",
    color: "black",
    textDecoration: "underline",
  },

  // Transfer details
  transferDetailsContainer: {
    paddingLeft: 100,
    display: "flex",
    flexDirection: "column",
    gap: 4,
    fontWeight: "bold",
  },
  transferDetailRow: {
    flexDirection: "row",
    marginBottom: 2,
  },
  transferDetailLabel: {
    fontSize: 10,
    width: 80,
  },
  transferDetailColon: {
    fontSize: 10,
    marginRight: 10,
  },
  transferDetailValue: {
    fontSize: 10,
  },

  // Tables
  table: {
    display: "flex",
    width: "auto",
    borderTopWidth: 1,
    borderLeftWidth: 1,
    borderColor: "#000",
    marginBottom: 20,
  },
  tableRow: {
    flexDirection: "row",
  },
  tableColHeader: {
    width: "50%",
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: "#000",
    backgroundColor: "#f0f0f0",
    padding: 5,
    textAlign: "center",
  },
  tableColHeaderBlack: {
    width: "50%",
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: "#000",
    backgroundColor: "#000000",
    padding: 5,
    textAlign: "center",
  },
  tableCol: {
    width: "50%",
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: "#000",
    padding: 5,
  },
  tableColAmount: {
    width: "50%",
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: "#000",
    padding: 5,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  tableCellHeader: {
    fontSize: 10,
    fontWeight: "bold",
  },
  tableCellHeaderWhite: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  tableCell: {
    fontSize: 10,
  },
  tableCellAmount: {
    fontSize: 10,
    fontWeight: "bold",
  },

  // Description
  descriptionText: {
    fontSize: 10,
    marginBottom: 10,
  },

  // Media clearing section
  mediaClearingContainer: {
    marginBottom: 20,
  },
  mediaClearingHeaderRow: {
    flexDirection: "row",
    marginBottom: 5,
  },
  mediaClearingDataRow: {
    flexDirection: "row",
    marginBottom: 5,
  },
  mediaClearingCol: {
    width: "25%",
  },
  mediaClearingColWithMargin: {
    width: "25%",
    marginLeft: 5,
  },
  mediaClearingHeaderText: {
    fontSize: 10,
    fontWeight: "bold",
    textDecoration: "underline",
  },
  mediaClearingLabelText: {
    fontSize: 10,
  },
  mediaClearingValueText: {
    fontSize: 10,
    textAlign: "right",
  },
  mediaClearingValueTextBold: {
    fontSize: 10,
    fontWeight: "bold",
    textAlign: "right",
  },
  mediaClearingValueCol: {
    width: "25%",
    marginLeft: 5,
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    borderBottomStyle: "dashed",
    color: "black",
    fontWeight: "bold",
  },

  // Transfer date
  transferDate: {
    flexDirection: "row",
    marginTop: 10,
  },
  transferDateText: {
    fontSize: 10,
    textAlign: "center",
  },

  // Signature section
  signatureSectionContainer: {
    flexDirection: "column",
    marginTop: 5,
    gap: 25,
  },
  signatureRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  signatureLabelCol: {
    fontSize: 10,
    width: 90,
  },
  signatureLine: {
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    width: 200,
    height: 5,
    marginLeft: 10,
  },
  signatureTextContainer: {
    flexDirection: "column",
  },
  signatureTextCenter: {
    alignItems: "center",
  },
  signatureText: {
    fontWeight: "bold",
    fontSize: 10,
    textAlign: "center",
  },

  // Utility styles
  bold: {
    fontWeight: "bold",
  },
  textSize10: {
    fontSize: 10,
  },
  textSize11: {
    fontSize: 11,
  },
  marginBottom2: {
    marginBottom: 2,
  },
  marginBottom6: {
    marginBottom: 6,
  },
  marginBottom20: {
    marginBottom: 20,
  },
  width80: {
    width: 80,
  },
  marginRight10: {
    marginRight: 10,
  },
});
