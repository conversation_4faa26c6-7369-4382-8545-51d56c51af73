import React from "react";
import { View, Text } from "@react-pdf/renderer";
import { styles } from "./styles";

const SIGNATURE_ITEMS = [
  { label: "Payment by:", hasText: true, text: "Transfer" },
  { label: "Checked by:", hasText: false },
  { label: "Approved by:", hasText: false },
] as const;

export const SignatureSection: React.FC = () => (
  <View style={styles.signatureSectionContainer}>
    {SIGNATURE_ITEMS.map((item, index) => (
      <View key={index} style={styles.signatureRow}>
        <Text style={styles.signatureLabelCol}>{item.label}</Text>
        <View style={styles.signatureTextContainer}>
          {item.hasText && (
            <View style={styles.signatureTextCenter}>
              <Text style={styles.signatureText}>{item.text}</Text>
            </View>
          )}
          <View style={styles.signatureLine}></View>
        </View>
      </View>
    ))}
  </View>
);
