import React from "react";
import { View, Text } from "@react-pdf/renderer";
import { styles } from "./styles";
import { PaymentRequestData, SUMMARY_ROWS, SUMMARY_COLUMNS } from "./constants";

interface SummaryTableProps {
  summary?: PaymentRequestData["summary"];
}

export const SummaryTable: React.FC<SummaryTableProps> = ({ summary }) => (
  <View style={styles.mediaClearingContainer}>
    {/* Header Row */}
    <View style={styles.mediaClearingHeaderRow}>
      <View style={styles.mediaClearingCol}>
        <Text style={styles.mediaClearingHeaderText}></Text>
      </View>
      {SUMMARY_COLUMNS.map((column) => (
        <View key={column.field} style={styles.mediaClearingCol}>
          <Text style={styles.mediaClearingHeaderText}>{column.label}</Text>
        </View>
      ))}
    </View>

    {/* Data Rows */}
    {SUMMARY_ROWS.map((row) => (
      <View key={row.field} style={styles.mediaClearingDataRow}>
        <View style={styles.mediaClearingCol}>
          <Text style={row.isBold ? [styles.mediaClearingLabelText, styles.bold] : styles.mediaClearingLabelText}>
            {row.label}
          </Text>
        </View>
        {SUMMARY_COLUMNS.map((column) => (
          <View
            key={`${row.field}-${column.field}`}
            style={row.hasUnderline ? styles.mediaClearingValueCol : styles.mediaClearingColWithMargin}
          >
            <Text style={row.isBold ? styles.mediaClearingValueTextBold : styles.mediaClearingValueText}>
              {summary?.[column.field]?.[row.field] || "0"}
            </Text>
          </View>
        ))}
      </View>
    ))}

    {/* Transfer Date */}
    <View style={styles.transferDate}>
      <View style={styles.mediaClearingCol}>
        <Text style={[styles.textSize10, styles.bold]}>Transfer Date</Text>
      </View>
      <View style={styles.mediaClearingCol}>
        <Text style={styles.transferDateText}>22 Jul 2025</Text>
      </View>
      <View style={styles.mediaClearingCol}>
        <Text style={styles.transferDateText}>26 Jul 2025</Text>
      </View>
    </View>
  </View>
);
