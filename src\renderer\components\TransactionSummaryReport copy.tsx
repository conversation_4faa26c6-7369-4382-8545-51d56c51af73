import React from "react";
import { Document, Page, Text, View, StyleSheet } from "@react-pdf/renderer";
import { ReportSummary, ReportService } from "../services/reportService";
import { formatDateYearMonthDay } from "renderer/utils/helper";
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#ffffff",
    padding: 20,
    fontSize: 9,
  },
  header: {
    marginBottom: 15,
    borderBottom: 1,
    borderBottomColor: "#333333",
    paddingBottom: 8,
  },
  title: {
    fontSize: 10,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 15,
    color: "#333333",
  },
  headerInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    fontSize: 5,
    color: "#666666",
  },
  section: {
    margin: 5,
    padding: 5,
    flexGrow: 1,
  },
  table: {
    width: "auto",
    marginBottom: 15,
  },
  tableRow: {
    flexDirection: "row",
  },
  tableColHeader: {
    borderTopStyle: "solid",
    borderTopWidth: 1,
    borderBottomStyle: "solid",
    borderBottomWidth: 1,
    backgroundColor: "#f8f9fa",
    padding: 4,
    fontSize: 5,
    fontWeight: "bold",
    textAlign: "center",
    color: "#2563eb",
    borderColor: "#cccccc",
  },
  tableCol: {
    padding: 4,
    fontSize: 5,
  },
  tableColDate: {
    width: "6%",
    padding: 4,
    fontSize: 5,
  },
  tableColAccount: {
    width: "12%",
    padding: 4,
    fontSize: 5,
  },
  tableColName: {
    width: "14%",
    padding: 4,
    fontSize: 5,
  },
  tableColMdr: {
    width: "5%",
    padding: 4,
    fontSize: 5,
  },
  tableColCount: {
    width: "6%",
    padding: 4,
    fontSize: 5,
  },
  tableColAmount: {
    width: "7%",
    padding: 4,
    fontSize: 5,
  },
  tableColType: {
    width: "6%",
    padding: 4,
    fontSize: 5,
  },
  tableCellCenter: {
    textAlign: "center",
  },
  tableCellRight: {
    textAlign: "right",
  },
  text: {
    fontSize: 5,
    lineHeight: 1.5,
    textAlign: "justify",
  },
  totalRow: {
    backgroundColor: "#e3f2fd",
    fontWeight: "bold",
  },
  grandTotalRow: {
    backgroundColor: "#bbdefb",
    fontWeight: "bold",
    fontSize: 5,
    color: "#2563eb",
  },
  footer: {
    position: "absolute",
    bottom: 20,
    left: 20,
    right: 20,
    textAlign: "center",
    fontSize: 5,
    color: "#666666",
    borderTop: "1 solid #cccccc",
    paddingTop: 8,
  },
});

interface TransactionSummaryReportProps {
  reportData: ReportSummary;
}

export const TransactionSummaryReport: React.FC<
  TransactionSummaryReportProps
> = ({ reportData }) => {
  // Generate date-grouped data with subtotals
  const dateGroupedData = ReportService.generateDateGroupedData(reportData.merchants);
  console.log("reportData",reportData)
  const chunkedData = [];
  const itemsPerPage = 18; // Increased for smaller fonts and better space usage

  for (let i = 0; i < dateGroupedData.length; i += itemsPerPage) {
    chunkedData.push(dateGroupedData.slice(i, i + itemsPerPage));
  }

  // Ensure we have at least one page
  if (chunkedData.length === 0) {
    chunkedData.push([]);
  }

  const renderTableHeader = () => (
    <View style={styles.tableRow}>
      <View style={[styles.tableColHeader, styles.tableColDate]}>
        <Text>Trx. Date</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColName]}>
        <Text>Name</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColMdr]}>
        <Text>MDR %</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColCount]}>
        <Text>NO Of TXN</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>Trx. Amt.</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>MDR. Amt.</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>Transfer Fee</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>Vat. Amt.</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>Net Amt.</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>Withhold Tax</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>Reimburse Fee</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>Service Fee</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>Business Tax</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColAmount]}>
        <Text>Net Amt. CUP</Text>
      </View>
      <View style={[styles.tableColHeader, styles.tableColType]}>
        <Text>Provider</Text>
      </View>
    </View>
  );

  // Unified function to render both merchant rows and subtotal rows
  const renderDataRow = (item: any, index: number) => {
    // Safety checks for data
    const safeText = (value: any) => {
      if (value === null || value === undefined) return "N/A";
      return String(value);
    };

    const safeNumber = (value: any) => {
      if (value === null || value === undefined || isNaN(value)) return 0;
      return Number(value);
    };

    // Handle subtotal rows
    if (item.isSubtotal) {
      return renderSubtotalRow(item, index);
    }

    // Handle regular merchant rows
    const refundTextStyle = item.tradeStatus === 'REFUND' ? { color: '#dc2626' } : { color: '#000' };
    
    return (
      <View style={styles.tableRow} key={`merchant-${index}-${item.merchantVat || "unknown"}`}>
        <View style={styles.tableColDate}>
          <Text style={[styles.tableCellCenter, refundTextStyle]}>
            {safeText(formatDateYearMonthDay(item.transactionDate))}
          </Text>
        </View>
        <View style={styles.tableColName}>
          <Text style={refundTextStyle}>
            {safeText(item.merchantName)}
            {item.tradeStatus === 'REFUND' ? ' (Refund)' : ''}
          </Text>
        </View>
        <View style={styles.tableColMdr}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.mdrRate))}
          </Text>
        </View>
        <View style={styles.tableColCount}>
          <Text style={[styles.tableCellCenter, refundTextStyle]}>
            {safeNumber(item.transactionCount)}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.totalAmount))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.mdrAmount))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.transferFee))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.vatAmount))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.netAmount))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.withholdTax))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.reimbursementFee))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.serviceFee))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.cupBusinessTaxFee || 0))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, refundTextStyle]}>
            {ReportService.formatNumber(safeNumber(item.finalNetAmount || 0))}
          </Text>
        </View>
        <View style={styles.tableColType}>
          <Text style={[styles.tableCellCenter, refundTextStyle]}>
            {safeText(item.channelType)}
          </Text>
        </View>
      </View>
    );
  };

  // Function to render different types of subtotal rows
  const renderSubtotalRow = (item: any, index: number) => {
    const safeNumber = (value: any) => {
      if (value === null || value === undefined || isNaN(value)) return 0;
      return Number(value);
    };

    let subtotalLabel = "";
    let backgroundColor = '#ffffff';

    if (item.subtotalType === 'daily') {
      subtotalLabel = "Sub Total :";
      backgroundColor = '#f3f4f6';
    } else if (item.subtotalType === 'bank') {
      // subtotalLabel = `${ReportService.formatNumber(safeNumber(item.totalAmount))} ${item.bankCode} Total`;
      subtotalLabel = `${item.bankCode} Total`;
      backgroundColor = '#e5e7eb';
    } else if (item.subtotalType === 'empty') {
      // Empty row for spacing - completely blank
      return (
        <View style={{ height: 20, width: '100%' }} key={`empty-${index}`}>
          <Text></Text>
        </View>
      );
    } else if (item.subtotalType === 'grand') {
      subtotalLabel = "Total :";
      backgroundColor = '#d1d5db';
    }

    return (
      <View style={[styles.tableRow, { backgroundColor }]} key={`subtotal-${index}-${item.subtotalType}`}>
        <View style={styles.tableColDate}>
          <Text style={styles.tableCellCenter}></Text>
        </View>
        <View style={styles.tableColName}>
          <Text style={[styles.tableCellCenter, { fontWeight: 'bold' }]}>
            {subtotalLabel}
          </Text>
        </View>
        <View style={styles.tableColMdr}>
          <Text style={styles.tableCellRight}></Text>
        </View>
        <View style={styles.tableColCount}>
          <Text style={[styles.tableCellCenter, { fontWeight: 'bold' }]}>
            {safeNumber(item.totalTransactions)}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalAmount))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalMdrAmount))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalTransferFee))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalVatAmount))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalNetAmount))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalWithholdTax))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalReimbursementFee))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalServiceFee))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalBusinessTax))}
          </Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={[styles.tableCellRight, { fontWeight: 'bold' }]}>
            {ReportService.formatNumber(safeNumber(item.totalFinalNetAmount))}
          </Text>
        </View>
        <View style={styles.tableColType}>
          <Text style={styles.tableCellCenter}></Text>
        </View>
      </View>
    );
  };

  return (
    <Document>
      {chunkedData.map((dataChunk, pageIndex) => (
        <Page
          key={pageIndex}
          size="A4"
          orientation="landscape"
          style={styles.page}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>
              Transaction Summary Report by Merchant ID with/without Withholding
              TAX
            </Text>
            <View style={styles.headerInfo}>
              <Text>Print Date: {reportData.reportDate}</Text>
              <Text>Time: {reportData.reportTime}</Text>
              <Text>
                Page: {pageIndex + 1} of {chunkedData.length}
              </Text>
            </View>
          </View>

          {/* Calculation Methodology - Show only on first page */}
          {/* {pageIndex === 0 && (
            <View
              style={{
                marginBottom: 10,
                padding: 8,
                backgroundColor: "#f8f9fa",
                border: "1 solid #dee2e6",
              }}
            >
              <Text
                style={{ fontSize: 6, fontWeight: "bold", marginBottom: 4 }}
              >
                Enhanced Financial Calculation Methodology with Separate Success/Refund Rows:
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • Each merchant displays TWO SEPARATE ROWS: one for SUCCESS transactions and one for REFUND transactions
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • SUCCESS Row: Positive amounts with normal fee calculations
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • REFUND Row: Negative amounts with reversed fee calculations
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • MDR Amount = Transaction Amount × (MDR% ÷ 100) [from merchant_wechat.wechat_rate]
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • Transfer Fee = Direct value [from merchant.transfer_fee]
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • VAT Amount = (Transfer Fee × VAT% ÷ 100) + (MDR Amount × VAT% ÷ 100) [from network_service.vat_percentage]
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • Net Amount = Transaction Amount - (MDR Amount + Transfer Fee + VAT Amount)
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • Withholding Tax = (MDR Amount × (Withholding Tax% ÷ 100)) + (Transfer Fee × (Withholding Tax% ÷ 100)) [from merchant.withholding_tax]
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • Reimbursement Fee = Transaction Amount × 0.5%
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • Service Fee = 0% for WeChat, 0.1% for other channels
              </Text>
              <Text style={{ fontSize: 5, marginBottom: 2 }}>
                • CUP Business Tax = 0% for WeChat, 0.05% for other channels
              </Text>
              <Text style={{ fontSize: 5 }}>
                • Final Net Amount = Transaction Amount - (Transfer Fee + Reimbursement Fee + Service Fee + CUP Business Tax)
              </Text>
            </View>
          )} */}

          {/* Table */}
          <View style={styles.table}>
            {renderTableHeader()}
            {dataChunk.map(renderDataRow)}
          </View>

          {/* Footer */}
          <Text style={styles.footer}>
            Generated by Transaction Management System - {reportData.reportDate}{" "}
            {reportData.reportTime}
          </Text>
        </Page>
      ))}
    </Document>
  );
};