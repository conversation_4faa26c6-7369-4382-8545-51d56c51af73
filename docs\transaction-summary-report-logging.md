# Automatic Transaction Summary Report Logging

## Overview
This feature implements automatic logging of Transaction Summary Reports into database tables whenever transaction files are uploaded through the Transaction Management upload screen. It provides a persistent audit trail and enables historical tracking of all transaction summaries.

## Database Schema

### Database Schema

#### `transaction_summary_report_detail`
This is the primary table storing all transaction summary data at the merchant level. The `transaction_summary_report` table is no longer used for new report generation.

```sql
CREATE TABLE transaction_summary_report_detail (
    id BIGSERIAL PRIMARY KEY,
    -- report_id BIGINT NOT NULL REFERENCES transaction_summary_report(id), -- Removed foreign key to main report table
    merchant_vat VARCHAR(100),
    merchant_name VARCHAR(255),
    transaction_date DATE,
    channel_type VARCHAR(50),
    transaction_count INTEGER DEFAULT 0,
    total_amount DECIMAL(18,2) DEFAULT 0,
    mdr_rate DECIMAL(8,4) DEFAULT 0,
    mdr_amount DECIMAL(18,2) DEFAULT 0,
    vat_percentage DECIMAL(8,4) DEFAULT 0,
    vat_amount DECIMAL(18,2) DEFAULT 0,
    net_amount DECIMAL(18,2) DEFAULT 0,
    withholding_tax_rate DECIMAL(8,4) DEFAULT 0,
    withhold_tax DECIMAL(18,2) DEFAULT 0,
    transfer_fee DECIMAL(18,2) DEFAULT 0,
    reimbursement_fee DECIMAL(18,2) DEFAULT 0,
    service_fee DECIMAL(18,2) DEFAULT 0,
    final_net_amount DECIMAL(18,2) DEFAULT 0,
    cup_business_tax_fee DECIMAL(18,2) DEFAULT 0,
    is_transfer SMALLINT DEFAULT 0 CHECK (is_transfer IN (0, 1)),
    create_by VARCHAR(100) DEFAULT 'SYSTEM',
    create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(100) DEFAULT 'SYSTEM',
    update_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
```

## Implementation Components

### 1. TransactionSummaryReportService
**File**: `src/main/services/transactionSummaryReportService.ts`

Main service class that handles:
- Generating report data using the same calculation logic as PDF reports
- Saving report data to database tables
- Financial calculations (MDR, VAT, withholding tax, fees)
- Merchant grouping and aggregation

**Key Methods**:
- `generateAndSaveReport()`: Main entry point for report generation
- `generateReportData()`: Creates report data structure
- `saveReportToDatabase()`: Persists data to database tables

### 2. Integration with Transaction Processing
**File**: `src/main/handler/transactionHandler.ts`

The report generation is automatically triggered in the `process-transaction-files` IPC handler:

```typescript
// After successful file processing
if (results.processedFiles > 0 && results.savedTransactions > 0) {
  const batchId = `BATCH_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const processedFileNames = filesToProcess
    .filter(file => file.processed)
    .map(file => file.fileName);

  const reportResult = await summaryReportService.generateAndSaveReport(
    batchId,
    processedFileNames,
    results.processedFiles,
    results.savedTransactions,
    currentUser
  );
}
```

### 3. New and Updated IPC Handlers
The following IPC handlers are available for transaction summary management:

#### `get-transaction-summary-details`
Retrieves paginated list of transaction summary details from `transaction_summary_report_detail` with filtering:
- Date range filtering (`startDate`, `endDate`)
- Merchant VAT (`merchantVat`)
- Channel type (`channelType`)
- Transfer status (`isTransfer`: 0 for pending, 1 for transferred, undefined for all)
- Pagination support (`page`, `pageSize`)

#### `get-transaction-summary-today`
Retrieves transaction summary details for records created today, with optional filters:
- Merchant VAT (`merchantVat`)
- Channel type (`channelType`)
- Transfer status (`isTransfer`)
- Pagination support

#### `get-transaction-summary-aggregates`
Fetches aggregated totals (e.g., total transferred, total pending, gross total, total MDR, VAT, withholding tax, transfer fees) and channel breakdown from `transaction_summary_report_detail` based on filters:
- Date range filtering (`startDate`, `endDate`)
- Merchant VAT (`merchantVat`)
- Channel type (`channelType`)

#### `update-transfer-status`
Updates the `is_transfer` status for a single record in `transaction_summary_report_detail`:
- `detailId`: ID of the record to update
- `isTransfer`: New status (0 for not transferred, 1 for transferred)
- `updatedBy`: User performing the update

#### `bulk-update-transfer-status`
Updates the `is_transfer` status for multiple records in `transaction_summary_report_detail` in a single operation:
- `detailIds`: Array of IDs to update
- `isTransfer`: New status (0 or 1)
- `updatedBy`: User performing the update

#### `get-transfer-status-report`
Retrieves a report of transaction summary details with transfer status, supporting:
- Date range filtering (`startDate`, `endDate`)
- Transfer status (`isTransfer`)
- Pagination support
- Includes merchant and report details

**Deprecated IPC Handlers:**
- `get-transaction-summary-reports`: Replaced by `get-transaction-summary-details`.
- `get-transaction-summary-report-details`: Replaced by `get-transaction-summary-details` for detail retrieval.
- `approve-transaction-summary-report`: Replaced by `approve-transaction-summary` which marks records as transferred based on filters.

### 4. Updated Transaction Summary Screen
**File**: `src/renderer/screens/transaction-summary.screen.tsx`

Enhanced to use real database data:
- Loads actual report data instead of mock data
- Implements real approval functionality
- Maintains fallback to mock data for demonstration

## Workflow Integration

### Automatic Report Generation Process

1. **File Upload**: User uploads transaction files via Transaction Management screen
2. **File Processing**: Files are processed and transactions saved to database
3. **Report Generation**: Automatically triggered after successful processing
4. **Data Calculation**: Uses same logic as PDF report generation:
   - Groups transactions by merchant VAT
   - Calculates MDR, VAT, withholding tax, fees
   - Computes grand totals and averages
5. **Database Storage**: Saves report and detail records
6. **Audit Trail**: Records batch ID, processed files, user, timestamp

### Financial Calculations
The system uses the same calculation methodology as the existing PDF reports:

- **MDR Amount** = Transaction Amount × (MDR% ÷ 100) [from merchant_wechat.wechat_rate]
- **VAT Amount** = MDR Amount × (VAT% ÷ 100) [from network_service.vat_tax_percent]
- **Net Amount** = Transaction Amount - MDR Amount
- **Withholding Tax** = MDR Amount × (Withholding Tax% ÷ 100) [from merchant.withholding_tax]
- **Reimbursement Fee** = Transaction Amount × 0.5%
- **Final Net Amount** = Total Amount - (Transfer Fee + Reimbursement Fee + Service Fee + CUP Business Tax)

## Benefits

### 1. Audit Trail
- Complete history of all transaction summaries
- Tracks who processed files and when
- Links reports to specific file processing batches

### 2. Data Consistency
- Uses identical calculation logic as PDF reports
- Ensures data integrity between different views
- Maintains referential integrity with foreign keys

### 3. Performance
- Indexed tables for fast querying
- Paginated data retrieval
- Efficient merchant grouping

### 4. Approval Workflow
- Status tracking (GENERATED → APPROVED)
- Approval audit trail with user and timestamp
- Integration with role-based permissions

## Usage Examples

### Query Recent Reports
```sql
SELECT * FROM transaction_summary_report 
WHERE report_date >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY create_dt DESC;
```

### Get Merchant Details for a Report
```sql
SELECT d.* FROM transaction_summary_report_detail d
JOIN transaction_summary_report r ON d.report_id = r.id
WHERE r.batch_id = 'BATCH_1234567890_abc123def';
```

### Find Approved Reports
```sql
SELECT * FROM transaction_summary_report
WHERE status = 'APPROVED'
AND approved_at >= '2025-07-01';
```

### Query Transfer Fee Status
```sql
-- Get all pending transfers
SELECT d.*, r.report_date, r.running_number
FROM transaction_summary_report_detail d
JOIN transaction_summary_report r ON d.report_id = r.id
WHERE d.is_transfer = 0
ORDER BY d.transaction_date DESC;

-- Get completed transfers for a specific date
SELECT d.*, r.report_date, r.running_number
FROM transaction_summary_report_detail d
JOIN transaction_summary_report r ON d.report_id = r.id
WHERE d.is_transfer = 1
AND d.transaction_date = '2025-07-19';

-- Summary of transfer status
SELECT
    is_transfer,
    CASE
        WHEN is_transfer = 0 THEN 'Pending Transfer'
        WHEN is_transfer = 1 THEN 'Transfer Completed'
    END as status_description,
    COUNT(*) as record_count,
    SUM(final_net_amount) as total_amount
FROM transaction_summary_report_detail
GROUP BY is_transfer;
```

## Migration Instructions

1. **Run Database Migration**:
   Ensure the `transaction_summary_report_detail` table is created. The `database_migration_create_transaction_summary_report.sql` script might still be relevant for initial setup, but note that the `transaction_summary_report` table is no longer actively used for new data.
   ```bash
   psql "your_connection_string" -f database_migration_create_transaction_summary_report.sql
   ```

2. **Restart Application**: The updated services and handlers will be automatically loaded.

3. **Test Workflow**:
   - Upload transaction files via Transaction Management.
   - Verify that transaction summary details are automatically generated and saved to the `transaction_summary_report_detail` table.
   - Check the Transaction Summary screen to ensure it displays real data from the `transaction_summary_report_detail` table and that the new filtering and transfer status update functionalities work as expected.

## Future Enhancements

1. **Report Scheduling**: Automatic daily/weekly report generation
2. **Email Notifications**: Send reports to stakeholders
3. **Data Export**: Export historical reports to Excel/PDF
4. **Advanced Analytics**: Trend analysis and reporting
5. **Report Templates**: Customizable report formats
