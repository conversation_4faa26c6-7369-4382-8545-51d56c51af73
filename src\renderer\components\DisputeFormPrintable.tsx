"use client"

interface DisputeFormData {
  requestDate: string
  requestId: string
  institutionName: string
  iin: string
  contactName: string
  phone: string
  fax: string
  email: string
  title: "Mr." | "Ms."
  cardNumber: string
  transactionDate: string
  transactionTime: string
  merchantId: string
  transactionAmount: string
  transactionType: string
  referenceNumber: string
  terminalId: string
  currencyCode: string
  merchantName: string
  disputedAmount: string
  amountType: "full" | "partial"
  disputeType: string[]
  reasonCode: string
  supportingDocs: "yes" | "no"
  pages: string
  additionalMessage: string
}

const defaultData: DisputeFormData = {
  requestDate: "25/07/01",
  requestId: "3180725",
  institutionName: "E-POS Service Company Limited",
  iin: "30340764",
  contactName: "Urawee Sit<PERSON>hai",
  phone: "(662) 8215459",
  fax: "",
  email: "<EMAIL>",
  title: "Ms.",
  cardNumber: "wxc06fb3d2635dcf8f",
  transactionDate: "27/06/2025",
  transactionTime: "16:23:21",
  merchantId: "668905944000009",
  transactionAmount: "76,333.00",
  transactionType: "42000027192025062745",
  referenceNumber: "*********",
  terminalId: "",
  currencyCode: "764",
  merchantName: "T Z W CO.,LTD",
  disputedAmount: "72,516.00",
  amountType: "full",
  disputeType: ["Representment (Credit Card)"],
  reasonCode: "",
  supportingDocs: "yes",
  pages: "1",
  additionalMessage: "-Refund diff amount to customer.",
}

export default function DisputeFormPrintable({ data = defaultData }: { data?: DisputeFormData }) {
  return (
    <div className="dispute-form-container">
      <style>{`
  @media print {
    .no-print { display: none !important; }
    .dispute-form-container {
      width: 100% !important;
      max-width: 100% !important;
      margin: 0 !important;
      padding: 10px !important;
      font-family: Arial, sans-serif !important;
      font-size: 9pt !important;
      line-height: 1.1 !important;
      color: black !important;
      background: white !important;
      box-sizing: border-box !important;
    }
    .form-page {
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      page-break-after: avoid !important;
      height: 100vh !important;
      display: flex !important;
      flex-direction: column !important;
    }
    .form-header {
      font-size: 12pt !important;
      font-weight: bold !important;
      text-align: center !important;
      margin-bottom: 8px !important;
    }
    .request-info {
      display: flex !important;
      justify-content: space-between !important;
      margin-bottom: 8px !important;
      padding-bottom: 4px !important;
      border-bottom: 1px solid black !important;
      font-size: 9pt !important;
    }
    .section {
      border: 1px solid black !important;
      margin-bottom: 6px !important;
      break-inside: avoid !important;
      flex-shrink: 0 !important;
    }
    .section-header {
      background-color: #f0f0f0 !important;
      padding: 3px 5px !important;
      font-weight: bold !important;
      font-size: 9pt !important;
      border-bottom: 1px solid black !important;
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
    }
    .section-content {
      padding: 4px 8px !important;
    }
    .form-row {
      display: flex !important;
      margin-bottom: 3px !important;
      gap: 8px !important;
    }
    .form-field {
      flex: 1 !important;
      display: flex !important;
      align-items: center !important;
      gap: 4px !important;
    }
    .field-label {
      font-size: 7pt !important;
      font-weight: bold !important;
      line-height: 1 !important;
      white-space: nowrap !important;
      min-width: 60px !important;
    }
    .field-value {
      font-size: 8pt !important;
      border-bottom: 1px solid #ccc !important;
      padding-bottom: 1px !important;
      min-height: 10px !important;
      line-height: 1.1 !important;
      flex: 1 !important;
    }
    .checkbox {
      width: 10px !important;
      height: 10px !important;
      border: 1px solid black !important;
      display: inline-block !important;
      text-align: center !important;
      margin-right: 4px !important;
      font-size: 7pt !important;
      line-height: 8px !important;
      vertical-align: middle !important;
    }
    .checkbox-row {
      display: flex !important;
      align-items: center !important;
      margin-bottom: 2px !important;
      font-size: 8pt !important;
    }
    .signature-section {
      display: flex !important;
      justify-content: space-between !important;
      margin-top: 8px !important;
      gap: 15px !important;
    }
    .signature-box {
      flex: 1 !important;
      height: 80px !important;
      border: 1px solid black !important;
      padding: 3px !important;
    }
    .dispute-type-section {
      margin-top: 4px !important;
      margin-bottom: 4px !important;
    }
    .dispute-type-label {
      font-size: 7pt !important;
      font-weight: bold !important;
      margin-bottom: 2px !important;
      display: block !important;
    }
    .dispute-checkboxes {
      display: grid !important;
      grid-template-columns: 1fr 1fr !important;
      gap: 2px !important;
      margin-bottom: 3px !important;
    }
    @page {
      size: A4 portrait !important;
      margin: 0.3in !important;
    }
    .processing-section {
      min-height: 80px !important;
    }
  }

  .dispute-form-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
    font-size: 11pt;
    line-height: 1.3;
    background: white;
  }

  .form-header {
    font-size: 16pt;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
  }

  .request-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid black;
    font-size: 11pt;
  }

  .section {
    border: 1px solid black;
    margin-bottom: 12px;
  }

  .section-header {
    background-color: #f0f0f0;
    padding: 6px;
    font-weight: bold;
    font-size: 11pt;
    border-bottom: 1px solid black;
  }

  .section-content {
    padding: 8px;
  }

  .form-row {
    display: flex;
    margin-bottom: 6px;
    gap: 12px;
  }

  .form-field {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .field-label {
    font-size: 9pt;
    font-weight: bold;
    white-space: nowrap;
    min-width: 80px;
  }

  .field-value {
    font-size: 10pt;
    border-bottom: 1px solid #ccc;
    padding-bottom: 2px;
    min-height: 14px;
    flex: 1;
  }

  .checkbox {
    width: 12px;
    height: 12px;
    border: 1px solid black;
    display: inline-block;
    text-align: center;
    margin-right: 5px;
    font-size: 8pt;
    line-height: 10px;
    vertical-align: middle;
  }

  .checkbox-row {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 10pt;
  }

  .signature-section {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    gap: 20px;
  }

  .signature-box {
    flex: 1;
    height: 120px;
    border: 1px solid black;
    padding: 5px;
  }

  .dispute-type-section {
    margin-top: 6px;
    margin-bottom: 6px;
  }

  .dispute-type-label {
    font-size: 9pt;
    font-weight: bold;
    margin-bottom: 4px;
    display: block;
  }

  .dispute-checkboxes {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4px;
    margin-bottom: 6px;
  }
  .processing-section {
    min-height: 100px;
  }
`}</style>

      <div className="form-page">
        {/* Header */}
        <div className="form-header">Exhibit 4 Dispute Resolution Form</div>

        {/* Request Information */}
        <div className="request-info">
          <span>Request Date (dd/mm/yy): {data.requestDate}</span>
          <span>Request ID: {data.requestId}</span>
        </div>

        {/* Institution Information */}
        <div className="section">
          <div className="section-header">Institution Information</div>
          <div className="section-content">
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Institution Name</div>
                <div className="field-value">{data.institutionName}</div>
              </div>
              <div className="form-field">
                <div className="field-label">IIN</div>
                <div className="field-value">{data.iin}</div>
              </div>
            </div>
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Contact Name</div>
                <div className="field-value">{data.contactName}</div>
              </div>
              <div className="form-field">
                <div className="field-label">Phone</div>
                <div className="field-value">{data.phone}</div>
              </div>
            </div>
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Fax</div>
                <div className="field-value">{data.fax}</div>
              </div>
              <div className="form-field">
                <div className="field-label">Email</div>
                <div className="field-value">{data.email}</div>
              </div>
            </div>
            <div className="checkbox-row">
              <span className="checkbox">{data.title === "Mr." ? "X" : ""}</span>
              <span>Mr.</span>
              <span className="checkbox" style={{ marginLeft: "20px" }}>
                {data.title === "Ms." ? "X" : ""}
              </span>
              <span>Ms.</span>
            </div>
          </div>
        </div>

        {/* Transaction Information */}
        <div className="section">
          <div className="section-header">Transaction Information</div>
          <div className="section-content">
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Card Number</div>
                <div className="field-value">{data.cardNumber}</div>
              </div>
              <div className="form-field">
                <div className="field-label">Transaction Date</div>
                <div className="field-value">{data.transactionDate}</div>
              </div>
            </div>
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Transaction Time</div>
                <div className="field-value">{data.transactionTime}</div>
              </div>
              <div className="form-field">
                <div className="field-label">Merchant ID</div>
                <div className="field-value">{data.merchantId}</div>
              </div>
            </div>
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Transaction</div>
                <div className="field-value">{data.transactionAmount}</div>
              </div>
              <div className="form-field">
                <div className="field-label">Transaction Type</div>
                <div className="field-value">{data.transactionType}</div>
              </div>
            </div>
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Reference Number</div>
                <div className="field-value">{data.referenceNumber}</div>
              </div>
              <div className="form-field">
                <div className="field-label">Terminal ID</div>
                <div className="field-value">{data.terminalId}</div>
              </div>
            </div>
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Currency Code</div>
                <div className="field-value">{data.currencyCode}</div>
              </div>
              <div className="form-field">
                <div className="field-label">Merchant Name</div>
                <div className="field-value">{data.merchantName}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Dispute Information */}
        <div className="section">
          <div className="section-header">Dispute Information</div>
          <div className="section-content">
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Disputed Amount</div>
                <div className="field-value">{data.disputedAmount}</div>
              </div>
              <div className="form-field">
                <div className="checkbox-row">
                  <span className="checkbox">{data.amountType === "full" ? "X" : ""}</span>
                  <span>Full Amount</span>
                </div>
                <div className="checkbox-row">
                  <span className="checkbox">{data.amountType === "partial" ? "X" : ""}</span>
                  <span>Partial Amount</span>
                </div>
              </div>
            </div>

            <div className="dispute-type-section">
              <span className="dispute-type-label">Dispute Resolution Type</span>
              <div className="dispute-checkboxes">
                <div className="checkbox-row">
                  <span className="checkbox">{data.disputeType.includes("Credit Adjustment") ? "X" : ""}</span>
                  <span>Credit Adjustment</span>
                </div>
                <div className="checkbox-row">
                  <span className="checkbox">{data.disputeType.includes("First Chargeback") ? "X" : ""}</span>
                  <span>First Chargeback</span>
                </div>
                <div className="checkbox-row">
                  <span className="checkbox">
                    {data.disputeType.includes("Debit Adjustment (Credit Card)") ? "X" : ""}
                  </span>
                  <span>Debit Adjustment (Credit Card)</span>
                </div>
                <div className="checkbox-row">
                  <span className="checkbox">
                    {data.disputeType.includes("Second Chargeback (Credit Card,Non-ATM)") ? "X" : ""}
                  </span>
                  <span>Second Chargeback (Credit Card,Non-ATM)</span>
                </div>
              </div>
              <div className="checkbox-row">
                <span className="checkbox">{data.disputeType.includes("Representment (Credit Card)") ? "X" : ""}</span>
                <span>Representment (Credit Card)</span>
              </div>
            </div>

            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Currency Code</div>
                <div className="field-value">{data.currencyCode}</div>
              </div>
              <div className="form-field">
                <div className="field-label">Reason Code</div>
                <div className="field-value">{data.reasonCode}</div>
              </div>
            </div>

            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Supporting Documents</div>
                <div className="checkbox-row">
                  <span className="checkbox">{data.supportingDocs === "yes" ? "X" : ""}</span>
                  <span>Yes</span>
                  <span className="checkbox" style={{ marginLeft: "20px" }}>
                    {data.supportingDocs === "no" ? "X" : ""}
                  </span>
                  <span>No</span>
                </div>
              </div>
              <div className="form-field">
                <div className="field-label">Pages</div>
                <div className="field-value">{data.pages}</div>
              </div>
            </div>

            <div className="form-field">
              <div className="field-label">Additional Message (Reasons for Dispute Resolution)</div>
              <div className="field-value">{data.additionalMessage}</div>
            </div>
          </div>
        </div>

        {/* Authorized Signature */}
        <div className="signature-section">
          <div className="signature-box">
            <div className="field-label">Authorized By</div>
            <div style={{ fontSize: "7pt", marginTop: "15px" }}>(Signature and/or Stamp)</div>
          </div>
        </div>

        {/* Processing Result */}
        <div className="section processing-section" style={{ marginTop: "15px", height: "200px" }}>
          <div className="section-header">Processing Result (CPU Use Only)</div>
          <div className="section-content">
            <div className="checkbox-row">
              <span className="checkbox"></span>
              <span>Not Processed</span>
              <span className="checkbox" style={{ marginLeft: "20px" }}></span>
              <span>Processed</span>
            </div>
            <div className="form-row">
              <div className="form-field">
                <div className="field-label">Processing Date</div>
                <div className="field-value"></div>
              </div>
              <div className="form-field">
                <div className="field-label">Processed by</div>
                <div className="field-value"></div>
              </div>
            </div>
            <div className="form-field">
              <div className="field-label">Message</div>
              <div className="field-value"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export { DisputeFormPrintable }
