import React, { useState, useCallback } from "react";
import { Modal } from "../modal";
import { Button } from "../button";
import { Tabs } from "../tabs";
import { safeIpcInvoke } from "../../utils/electron";
import { useAuth } from "../../contexts/AuthContext";
import { useNotification } from "../../contexts/NotificationContext";
import { useMasterDataAccess } from "../../hooks/useRoleAccess";
import type {
  Merchant,
  MerchantBank,
  MerchantWechat,
  MerchantUnionpay,
  Bank,
  Group,
  Zone,
  Product,
  Category
} from "../../types/merchant";

// Validation types
interface ValidationError {
  field: string;
  message: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Merchant Details Tab
import { MerchantDetailsTab } from "./MerchantDetailsTab";

// Bank Accounts Tab
import { MerchantBankTab } from "./MerchantBankTab";

// WeChat Tab
import { MerchantWechatTab } from "./MerchantWechatTab";

// UnionPay Tab
import { MerchantUnionpayTab } from "./MerchantUnionpayTab";

interface MerchantModalProps {
  isOpen: boolean;
  onClose: () => void;
  editingMerchant: Merchant | null;
  formData: Omit<Merchant, "merchant_id" | "create_dt" | "update_dt">;
  onInputChange: (field: keyof Merchant, value: any) => void;
  merchantBanks: MerchantBank[];
  setMerchantBanks: React.Dispatch<React.SetStateAction<MerchantBank[]>>;
  merchantWechat: MerchantWechat | null;
  setMerchantWechat: React.Dispatch<React.SetStateAction<MerchantWechat | null>>;
  merchantUnionpay: MerchantUnionpay | null;
  setMerchantUnionpay: React.Dispatch<React.SetStateAction<MerchantUnionpay | null>>;
  banks: Bank[];
  groups: Group[];
  zones: Zone[];
  products: Product[];
  categories: Category[];
  mainMerchants: {merchant_id: number, merchant_name: string}[];
  onSubmit: (e: React.FormEvent) => Promise<void>;
  onRefresh: () => Promise<void>;
  viewOnly?: boolean; // New prop for view-only mode
}

export function MerchantModal({
  isOpen,
  onClose,
  editingMerchant,
  formData,
  onInputChange,
  merchantBanks,
  setMerchantBanks,
  merchantWechat,
  setMerchantWechat,
  merchantUnionpay,
  setMerchantUnionpay,
  banks,
  groups,
  zones,
  products,
  categories,
  mainMerchants,
  onSubmit,
  onRefresh,
  viewOnly = false
}: MerchantModalProps) {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const { hasWriteAccess } = useMasterDataAccess();
  const [activeTab, setActiveTab] = useState("details");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);

  // Determine if the modal should be in read-only mode
  const isReadOnly = viewOnly || !hasWriteAccess;

  // Validation function
  const validateFields = useCallback((): ValidationResult => {
    const errors: ValidationError[] = [];

    // Required field: Merchant Name
    if (!formData.merchant_name?.trim()) {
      errors.push({
        field: 'merchant_name',
        message: 'Merchant name is required'
      });
    }

    // Required field: Merchant Type
    if (!formData.merchant_type) {
      errors.push({
        field: 'merchant_type',
        message: 'Merchant type is required'
      });
    }

    // Required field: Parent Merchant for sub merchants
    if (formData.merchant_type === 'sub' && !formData.parent_merchant_id) {
      errors.push({
        field: 'parent_merchant_id',
        message: 'Parent merchant is required for sub merchants'
      });
    }

    // Required field: WeChat Merchant ID
    if (!formData.merchant_id_wechat?.trim()) {
      errors.push({
        field: 'merchant_id_wechat',
        message: 'WeChat merchant ID is required'
      });
    }

    // Email validation (if provided)
    if (formData.email && formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        errors.push({
          field: 'email',
          message: 'Please enter a valid email address'
        });
      }
    }

    // Contact email validation (if provided)
    if (formData.contact_email && formData.contact_email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.contact_email)) {
        errors.push({
          field: 'contact_email',
          message: 'Please enter a valid contact email address'
        });
      }
    }

    // Phone number validation (if provided)
    if (formData.phone && formData.phone.trim()) {
      const phoneRegex = /^[\d\s\-\+\(\)]+$/;
      if (!phoneRegex.test(formData.phone)) {
        errors.push({
          field: 'phone',
          message: 'Please enter a valid phone number'
        });
      }
    }

    // Contact phone validation (if provided)
    if (formData.contact_phone && formData.contact_phone.trim()) {
      const phoneRegex = /^[\d\s\-\+\(\)]+$/;
      if (!phoneRegex.test(formData.contact_phone)) {
        errors.push({
          field: 'contact_phone',
          message: 'Please enter a valid contact phone number'
        });
      }
    }

    // Numeric field validations
    if (formData.rate_min_transfer !== undefined && formData.rate_min_transfer < 0) {
      errors.push({
        field: 'rate_min_transfer',
        message: 'Minimum transfer rate cannot be negative'
      });
    }

    if (formData.transfer_fee !== undefined && formData.transfer_fee < 0) {
      errors.push({
        field: 'transfer_fee',
        message: 'Transfer fee cannot be negative'
      });
    }

    if (formData.settlement_fee !== undefined && formData.settlement_fee < 0) {
      errors.push({
        field: 'settlement_fee',
        message: 'Settlement fee cannot be negative'
      });
    }

    if (formData.withholding_tax !== undefined && (formData.withholding_tax < 0 || formData.withholding_tax > 100)) {
      errors.push({
        field: 'withholding_tax',
        message: 'Withholding tax must be between 0 and 100 percent'
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, [formData]);

  // Handle direct submission without form event
  const handleSubmit = async () => {
    setIsSubmitting(true);
    setValidationErrors([]);

    try {
      // Validate fields first
      const validation = validateFields();

      if (!validation.isValid) {
        setValidationErrors(validation.errors);
        showNotification(
          `Please fix the following errors: ${validation.errors.map(e => e.message).join(', ')}`,
          "error"
        );
        return;
      }

      // Create a mock form event for compatibility with existing onSubmit
      const mockEvent = {
        preventDefault: () => {},
        target: {},
        currentTarget: {}
      } as React.FormEvent;

      await onSubmit(mockEvent);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get validation error for a specific field
  const getFieldError = useCallback((fieldName: string): string | undefined => {
    const error = validationErrors.find(err => err.field === fieldName);
    return error?.message;
  }, [validationErrors]);

  // Add bank account
  const handleAddBank = async (bankData: Omit<MerchantBank, "merchant_bank_id" | "create_dt" | "update_dt">) => {
    if (!editingMerchant?.merchant_id) {
      showNotification(
        "Please save merchant details first before adding bank accounts",
        "error"
      );
      return;
    }

    try {
      const response = await safeIpcInvoke("create-merchant-bank", {
        ...bankData,
        merchant_id: editingMerchant.merchant_id,
        create_by: user?.user_name || "SYSTEM",
      });

      if (response.success) {
        showNotification(
          "Bank account added successfully",
          "success"
        );

        // Refresh bank accounts
        const bankResponse = await safeIpcInvoke("get-merchant-banks", editingMerchant.merchant_id);
        if (bankResponse.success) {
          setMerchantBanks(bankResponse.data || []);
        }
      } else {
        showNotification(
          response.message || "Failed to add bank account",
          "error"
        );
      }
    } catch (error) {
      console.error("Error adding bank account:", error);
      showNotification(
        "Error adding bank account",
        "error"
      );
    }
  };

  // Update bank account
  const handleUpdateBank = async (bankId: number, bankData: Omit<MerchantBank, "merchant_bank_id" | "create_dt" | "create_by">) => {
    try {
      const response = await safeIpcInvoke("update-merchant-bank", bankId, {
        ...bankData,
        update_by: user?.user_name || "SYSTEM",
      });

      if (response.success) {
        showNotification(
          "Bank account updated successfully",
          "success"
        );

        // Refresh bank accounts
        if (editingMerchant?.merchant_id) {
          const bankResponse = await safeIpcInvoke("get-merchant-banks", editingMerchant.merchant_id);
          if (bankResponse.success) {
            setMerchantBanks(bankResponse.data || []);
          }
        }
      } else {
        showNotification(
          response.message || "Failed to update bank account",
          "error"
        );
      }
    } catch (error) {
      console.error("Error updating bank account:", error);
      showNotification(
        "Error updating bank account",
        "error"
      );
    }
  };

  // Delete bank account
  const handleDeleteBank = async (bankId: number) => {
    if (window.confirm("Are you sure you want to delete this bank account?")) {
      try {
        const response = await safeIpcInvoke("delete-merchant-bank", bankId);

        if (response.success) {
          showNotification(
            "Bank account deleted successfully",
            "success"
          );

          // Refresh bank accounts
          if (editingMerchant?.merchant_id) {
            const bankResponse = await safeIpcInvoke("get-merchant-banks", editingMerchant.merchant_id);
            if (bankResponse.success) {
              setMerchantBanks(bankResponse.data || []);
            }
          }
        } else {
          showNotification(
            response.message || "Failed to delete bank account",
            "error"
          );
        }
      } catch (error) {
        console.error("Error deleting bank account:", error);
        showNotification(
          "Error deleting bank account",
          "error"
        );
      }
    }
  };

  // Save WeChat settings
  const handleSaveWechat = async (wechatData: Omit<MerchantWechat, "merchant_wechat_id" | "create_dt" | "update_dt">) => {
    if (!editingMerchant?.merchant_id) {
      showNotification(
        "Please save merchant details first before adding WeChat settings",
        "error"
      );
      return;
    }

    try {
      let response;

      if (merchantWechat?.merchant_wechat_id) {
        // Update existing WeChat settings
        response = await safeIpcInvoke("update-merchant-wechat", merchantWechat.merchant_wechat_id, {
          ...wechatData,
          update_by: user?.user_name || "SYSTEM",
        });
      } else {
        // Create new WeChat settings
        response = await safeIpcInvoke("create-merchant-wechat", {
          ...wechatData,
          merchant_id: editingMerchant.merchant_id,
          create_by: user?.user_name || "SYSTEM",
        });
      }

      if (response.success) {
        showNotification(
          "WeChat settings saved successfully",
          "success"
        );
        setMerchantWechat(response.data);
      } else {
        showNotification(
          response.message || "Failed to save WeChat settings",
          "error"
        );
      }
    } catch (error) {
      console.error("Error saving WeChat settings:", error);
      showNotification(
        "Error saving WeChat settings",
        "error"
      );
    }
  };

  // Save UnionPay settings
  const handleSaveUnionpay = async (unionpayData: Omit<MerchantUnionpay, "merchant_unionpay_id" | "create_dt" | "update_dt">) => {
    if (!editingMerchant?.merchant_id) {
      showNotification(
        "Please save merchant details first before adding UnionPay settings",
        "error"
      );
      return;
    }

    try {
      let response;

      if (merchantUnionpay?.merchant_unionpay_id) {
        // Update existing UnionPay settings
        response = await safeIpcInvoke("update-merchant-unionpay", merchantUnionpay.merchant_unionpay_id, {
          ...unionpayData,
          update_by: user?.user_name || "SYSTEM",
        });
      } else {
        // Create new UnionPay settings
        response = await safeIpcInvoke("create-merchant-unionpay", {
          ...unionpayData,
          merchant_id: editingMerchant.merchant_id,
          create_by: user?.user_name || "SYSTEM",
        });
      }

      if (response.success) {
        showNotification(
          "UnionPay settings saved successfully",
          "success"
        );
        setMerchantUnionpay(response.data);
      } else {
        showNotification(
          response.message || "Failed to save UnionPay settings",
          "error"
        );
      }
    } catch (error) {
      console.error("Error saving UnionPay settings:", error);
      showNotification(
        "Error saving UnionPay settings",
        "error"
      );
    }
  };

  const tabs = [
    {
      id: "details",
      label: "Merchant Details",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      ),
      content: (
        <MerchantDetailsTab
          formData={formData}
          onInputChange={onInputChange}
          editingMerchant={editingMerchant}
          groups={groups}
          zones={zones}
          products={products}
          categories={categories}
          mainMerchants={mainMerchants}
          readOnly={isReadOnly}
          validationErrors={validationErrors}
        />
      ),
    },
    {
      id: "banks",
      label: "Bank Accounts",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
      ),
      content: (
        <MerchantBankTab
          merchantBanks={merchantBanks}
          banks={banks}
          onAddBank={handleAddBank}
          onUpdateBank={handleUpdateBank}
          onDeleteBank={handleDeleteBank}
          editingMerchant={editingMerchant}
          readOnly={isReadOnly}
        />
      ),
    },
    {
      id: "wechat",
      label: "WeChat Pay",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      ),
      content: (
        <MerchantWechatTab
          merchantWechat={merchantWechat}
          onSave={handleSaveWechat}
          editingMerchant={editingMerchant}
          readOnly={isReadOnly}
        />
      ),
    },
    {
      id: "unionpay",
      label: "UnionPay",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z" />
        </svg>
      ),
      content: (
        <MerchantUnionpayTab
          merchantUnionpay={merchantUnionpay}
          onSave={handleSaveUnionpay}
          editingMerchant={editingMerchant}
          readOnly={isReadOnly}
        />
      ),
    },
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size={isReadOnly ? "xl" : "xl"}
    >
      <div className={isReadOnly ? "max-w-[800px]" : "max-w-[800px]"}>
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-2">
            {isReadOnly && (
              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
            )}
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {isReadOnly
                  ? editingMerchant?.merchant_name || "View Merchant Details"
                  : editingMerchant
                    ? "Edit Merchant"
                    : "Create New Merchant"
                }
              </h2>
              {isReadOnly && editingMerchant && (
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm text-gray-600">ID: #{editingMerchant.merchant_id}</span>
                  <span className="text-gray-400">•</span>
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                    editingMerchant.merchant_type === "main"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-purple-100 text-purple-800"
                  }`}>
                    {editingMerchant.merchant_type === "main" ? "Main Merchant" : "Sub Merchant"}
                  </span>
                  <span className="text-gray-400">•</span>
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                    editingMerchant.active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                  }`}>
                    {editingMerchant.active ? "Active" : "Inactive"}
                  </span>
                </div>
              )}
            </div>
          </div>
          <p className="text-gray-600">
            {isReadOnly
              ? "View comprehensive merchant information and payment configurations"
              : editingMerchant
                ? "Update merchant information and payment settings"
                : "Add a new merchant to the system"}
          </p>
        </div>

        <div>
          {/* Show tabs for edit/create mode, single view for read-only mode */}
          {isReadOnly ? (
            <div className="space-y-8">
              {/* All content in one view for read-only mode */}
              {tabs.map((tab) => (
                <div key={tab.id}>
                  <div className="flex items-center gap-3 mb-4 pb-3 border-b border-gray-200">
                    <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-lg">
                      {tab.icon}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{tab.label}</h3>
                  </div>
                  <div className="ml-11">
                    {tab.content}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <Tabs
              tabs={tabs}
              defaultTab="details"
              onChange={setActiveTab}
              className="mb-6"
            />
          )}

          <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
            <Button
              type="button"
              onClick={onClose}
              variant="secondary"
              disabled={isSubmitting}
            >
              {isReadOnly ? "Close" : "Cancel"}
            </Button>
            {!isReadOnly && (
              <Button
                type="button"
                onClick={handleSubmit}
                variant="primary"
                disabled={isSubmitting}
                className="min-w-[120px]"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </div>
                ) : (
                  editingMerchant ? "Update Merchant" : "Create Merchant"
                )}
              </Button>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
}
