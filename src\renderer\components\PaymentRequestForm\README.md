# PaymentRequestForm - Refactored Structure

## Overview
The PaymentRequestForm component has been refactored into a modular, maintainable structure with improved code organization and reusability.

## File Structure

```
PaymentRequestForm/
├── index.ts                  # Main export file
├── constants.ts             # Data interfaces, default values, and configuration
├── styles.ts               # Centralized StyleSheet definitions
├── TransferDetails.tsx     # Transfer account details component
├── TransactionTable.tsx    # Transaction details table component
├── SummaryTable.tsx        # Summary comparison table component
└── SignatureSection.tsx   # Signature lines component
```

## Key Improvements

### 1. **Modular Architecture**
- Split large monolithic component into focused, single-responsibility components
- Each component handles a specific section of the PDF
- Easier to test, maintain, and modify individual sections

### 2. **Eliminated Code Duplication**
- Removed duplicate styles (consolidated 70+ styles to 50+ optimized styles)
- Eliminated redundant "Inline" style variants
- Created reusable utility styles

### 3. **Centralized Configuration**
- Moved all constants, interfaces, and default data to `constants.ts`
- Created configuration arrays for tables (`TRANSACTION_ROWS`, `SUMMARY_ROWS`, `SUMMARY_COLUMNS`)
- Enables easy modification of table structure without touching component code

### 4. **Improved Type Safety**
- Better TypeScript interfaces with proper optional properties
- Consistent typing across all components
- Proper type exports and imports

### 5. **Enhanced Maintainability**
- Clear separation of concerns
- Easier to locate and modify specific functionality
- Reduced cognitive load when working with the codebase

## Usage

### Basic Usage
```tsx
import { PaymentRequestPDF, usePaymentRequestPDF } from './components/PaymentRequestForm';

// Using the PDF component
<PaymentRequestPDF data={myData} />

// Using the hook
const { generatePDF, downloadPDF, openPDFInNewWindow } = usePaymentRequestPDF();
```

### Individual Components
```tsx
import { 
  TransferDetails, 
  TransactionTable, 
  SummaryTable 
} from './components/PaymentRequestForm';

// Use individual components in custom layouts
<TransferDetails fromAccount={accountData} />
<TransactionTable transactions={transactionData} />
<SummaryTable summary={summaryData} />
```

## Components

### TransferDetails
Renders the "Transfer from" section with account information.
- **Props**: `fromAccount?: PaymentRequestData["fromAccount"]`

### TransactionTable
Renders the transaction details table with black headers.
- **Props**: `transactions?: PaymentRequestData["transactions"]`
- **Features**: Dynamic row generation from `TRANSACTION_ROWS` configuration

### SummaryTable
Renders the three-column summary comparison table.
- **Props**: `summary?: PaymentRequestData["summary"]`
- **Features**: Dynamic column/row generation from configuration arrays

### SignatureSection
Renders the signature lines with labels.
- **Props**: None (self-contained)
- **Features**: Configurable signature items

## Configuration

### Adding New Transaction Rows
Edit `TRANSACTION_ROWS` in `constants.ts`:
```tsx
export const TRANSACTION_ROWS = [
  { label: "New Row", field: "newField" },
  { label: "Static Row", field: null, value: "Fixed Value" },
  // ... existing rows
] as const;
```

### Adding New Summary Rows
Edit `SUMMARY_ROWS` in `constants.ts`:
```tsx
export const SUMMARY_ROWS = [
  { 
    label: "New Summary Item", 
    field: "newField", 
    hasUnderline: true, 
    isBold: false 
  },
  // ... existing rows
] as const;
```

### Modifying Styles
All styles are centralized in `styles.ts`:
```tsx
export const styles = StyleSheet.create({
  newStyle: {
    // your styles here
  },
  // ... existing styles
});
```

## Benefits

1. **Reduced Bundle Size**: Eliminated duplicate code and unused styles
2. **Improved Performance**: Smaller component trees and optimized re-renders
3. **Better Developer Experience**: Clear file organization and focused components
4. **Enhanced Testability**: Each component can be tested in isolation
5. **Easier Customization**: Configuration-driven approach for modifications
6. **Future-Proof**: Modular structure supports easy feature additions

## Migration Notes

The refactored components maintain 100% backward compatibility with the original API. No changes required for existing implementations using `PaymentRequestPDF` or `usePaymentRequestPDF`.
