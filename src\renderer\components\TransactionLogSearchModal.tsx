import React, { useState, useEffect } from "react";
import {
  Search,
  Calendar,
  User,
  FileText,
  X,
  Eye,
  Filter,
  Refresh<PERSON><PERSON>,
  Printer,
  BarChart3,
} from "lucide-react";
import { usePaymentRequestPDF, PaymentRequestData } from "./PaymentRequestForm";
import { TransactionSummaryReportViewer } from "./TransactionSummaryReportViewer";
import { useTransactionSummaryReport } from "../hooks/useTransactionSummaryReport";
import { formatCurrencyAmount, formatCurrencyTHB, formatDateLocale, formatDateGB } from "../utils/helper";

interface TransactionPaymentForm {
  id: number;
  trx_no: string;
  bank_bbl_amount: number;
  bank_oth_amount: number;
  sum_amount: number;
  vat_amount: number;
  withhold_amount: number;
  mdr_amount: number;
  net_amount: number;
  approve_dt: string;
  active: boolean;
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
}

interface TransactionLogSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTransaction?: (transaction: TransactionPaymentForm) => void;
}

interface SearchFilters {
  search: string;
  startDate: string;
  endDate: string;
  trxNo: string;
  createdBy: string;
  sortBy: string;
  sortOrder: "ASC" | "DESC";
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export const TransactionLogSearchModal: React.FC<
  TransactionLogSearchModalProps
> = ({ isOpen, onClose, onSelectTransaction }) => {
  const [transactions, setTransactions] = useState<TransactionPaymentForm[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [selectedTransaction, setSelectedTransaction] =
    useState<TransactionPaymentForm | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [detailsData, setDetailsData] = useState<any>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    pageSize: 20,
    hasNextPage: false,
    hasPreviousPage: false,
  });

  const [filters, setFilters] = useState<SearchFilters>({
    search: "",
    startDate: "",
    endDate: "",
    trxNo: "",
    createdBy: "",
    sortBy: "create_dt",
    sortOrder: "DESC",
  });

  const [showFilters, setShowFilters] = useState(false);

  // PDF generation hook
  const { generatePDF, downloadPDF, openPDFInNewWindow } =
    usePaymentRequestPDF();

  // Transaction Summary Report hook
  const {
    reportData: transactionSummaryReportData,
    isLoading: generatingReport,
    generateReportFromTransaction,
    clearReport
  } = useTransactionSummaryReport();

  const [showTransactionSummaryReport, setShowTransactionSummaryReport] = useState(false);

  // Helper function to create summary section data
  const createSummarySection = (
    count: number,
    totalAmount: number,
    mdrAmount: number,
    transferFee: number,
    vatAmount: number
  ) => {
    const netAmount = totalAmount - (mdrAmount + transferFee + vatAmount);
    return {
      totalTransactions: count.toString(),
      totalAmount: formatCurrencyAmount(totalAmount),
      lessDiscount: formatCurrencyAmount(mdrAmount),
      transferFee: formatCurrencyAmount(transferFee),
      vat: formatCurrencyAmount(vatAmount),
      netAmount: formatCurrencyAmount(netAmount),
    };
  };

  // Search function
  const searchTransactions = async (page: number = 1) => {
    if (!isOpen) return;

    setLoading(true);
    try {
      const searchParams = {
        page,
        pageSize: pagination.pageSize,
        ...filters,
      };

      if (!window.electronAPI?.searchTransactionPaymentForms) {
        throw new Error("Search function not available");
      }

      const result = await window.electronAPI.searchTransactionPaymentForms(
        searchParams
      );

      if (result.success) {
        setTransactions(result.data || []);
        if (result.pagination) {
          setPagination(result.pagination);
        }
      } else {
        console.error("Failed to search transactions:", result.error);
        setTransactions([]);
      }
    } catch (error) {
      console.error("Error searching transactions:", error);
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  };

  // Load transaction details
  const loadTransactionDetails = async (trxNo: string) => {
    setLoading(true);
    try {
      if (!window.electronAPI?.getTransactionPaymentFormDetails) {
        throw new Error("Details function not available");
      }

      const result = await window.electronAPI.getTransactionPaymentFormDetails(
        trxNo
      );
      console.log("result", result);
      if (result.success) {
        setDetailsData(result.data);
        setShowDetails(true);
      } else {
        console.error("Failed to load transaction details:", result.error);
      }
    } catch (error) {
      console.error("Error loading transaction details:", error);
    } finally {
      setLoading(false);
    }
  };

  // Generate and download payment request form PDF
  const generatePaymentRequestPDF = async (trxNo: string) => {
    setLoading(true);
    try {
      if (!window.electronAPI?.getTransactionPaymentFormDetails) {
        throw new Error("Details function not available");
      }

      const result = await window.electronAPI.getTransactionPaymentFormDetails(
        trxNo
      );

      if (result.success && result.data) {
        const paymentForm = result.data.paymentForm;
        const transactionDetails = result.data.transactionDetails;
        console.log("paymentForm", paymentForm);

        // Map the transaction data to PaymentRequestData format
        const paymentRequestData: PaymentRequestData = {
          date: formatDateGB(),
          amount: formatCurrencyAmount(paymentForm.net_amount),
          fromAccount: {
            name: "E-POS Service Company Limited",
            type: "Saving Account",
            bank: "BBL",
            branch: "Head Office",
            accountNo: "224-0-33223-5",
          },
          transactions: {
            merchantGross: formatCurrencyAmount(paymentForm.sum_amount),
            merchantGrossOth: formatCurrencyAmount(paymentForm.bank_oth_amount),
            merchantNet: formatCurrencyAmount(paymentForm.net_amount),
            discountEarned: formatCurrencyAmount(paymentForm.mdr_amount),
            vat: formatCurrencyAmount(paymentForm.vat_amount),
            withholdingTax: formatCurrencyAmount(paymentForm.withhold_amount),
          },
          description: `With Holding Tax 3% Amount THB ${formatCurrencyAmount(
            paymentForm.withhold_amount
          )}`,
          dateRange: {
            from: formatDateGB(),
            to: formatDateGB(),
          },
          summary: {
            directCreditBBL: createSummarySection(
              paymentForm.bank_bbl_count,
              parseFloat(paymentForm.bank_bbl_amount),
              parseFloat(paymentForm.mdr_amount_bbl),
              parseFloat(paymentForm.transfer_fee_bbl),
              parseFloat(paymentForm.vat_amount_bbl)
            ),
            mediaClearing: createSummarySection(
              paymentForm.bank_oth_count,
              parseFloat(paymentForm.bank_oth_amount),
              parseFloat(paymentForm.mdr_amount_oth),
              parseFloat(paymentForm.transfer_fee_oth),
              parseFloat(paymentForm.vat_amount_oth)
            ),
            sumTotal: (() => {
              const totalAmount =
                parseFloat(paymentForm.bank_bbl_amount) +
                parseFloat(paymentForm.bank_oth_amount);
              const lessDiscount = parseFloat(paymentForm.mdr_amount);
              const transferFee = 0;
              const vat = parseFloat(paymentForm.vat_amount);
              const netAmount = parseFloat(paymentForm.net_amount);
              return {
                totalTransactions: (
                  paymentForm.bank_bbl_count + paymentForm.bank_oth_count
                ).toString(),
                totalAmount: formatCurrencyAmount(totalAmount),
                lessDiscount: formatCurrencyAmount(lessDiscount),
                transferFee: formatCurrencyAmount(transferFee),
                vat: formatCurrencyAmount(vat),
                netAmount: formatCurrencyAmount(netAmount),
              };
            })(),
          },
        };

        // Preview the PDF in a new window
        await openPDFInNewWindow(paymentRequestData);

        console.log("Payment request PDF preview opened successfully");
      } else {
        console.error(
          "Failed to load transaction data for PDF generation:",
          result.error
        );
      }
    } catch (error) {
      console.error("Error generating payment request PDF:", error);
    } finally {
      setLoading(false);
    }
  };

  // Generate Transaction Summary Report from current transaction details
  const generateTransactionSummaryReport = async (trxNo: string) => {
    try {
      await generateReportFromTransaction(trxNo);
      setShowTransactionSummaryReport(true);
    } catch (error) {
      console.error("Error generating transaction summary report:", error);
    }
  };

  // Handle download of transaction summary report
  const handleDownloadTransactionSummaryReport = async () => {
    if (!transactionSummaryReportData) return;
    // This will be handled by the TransactionSummaryReportViewer component
  };

  // Format currency - using utility function
  const formatCurrency = formatCurrencyTHB;

  // Format date - using utility function  
  const formatDate = formatDateLocale;

  // Handle filter changes
  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  // Handle search
  const handleSearch = () => {
    searchTransactions(1);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setFilters({
      search: "",
      startDate: "",
      endDate: "",
      trxNo: "",
      createdBy: "",
      sortBy: "create_dt",
      sortOrder: "DESC",
    });
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      searchTransactions(newPage);
    }
  };

  // Handle transaction selection
  const handleSelectTransaction = (transaction: TransactionPaymentForm) => {
    // setSelectedTransaction(transaction);
    // if (onSelectTransaction) {
    //   onSelectTransaction(transaction);
    // }
  };

  // Load initial data when modal opens
  useEffect(() => {
    if (isOpen) {
      searchTransactions(1);
    }
  }, [isOpen]);

  // Handle search on filter changes
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (isOpen) {
        searchTransactions(1);
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [filters.search, filters.trxNo]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="flex items-center space-x-3">
            <FileText className="w-6 h-6" />
            <h2 className="text-xl font-semibold">
              Search Transaction Payment Forms
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-blue-800 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-wrap gap-4 mb-4">
            {/* Main Search */}
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search by transaction number, creator..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Transaction Number Filter */}
            <div className="min-w-48">
              <input
                type="text"
                placeholder="Transaction Number"
                value={filters.trxNo}
                onChange={(e) => handleFilterChange("trxNo", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter Toggle Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-2 rounded-lg border transition-colors flex items-center space-x-2 ${
                showFilters
                  ? "bg-blue-600 text-white border-blue-600"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
            </button>

            {/* Search Button */}
            <button
              onClick={handleSearch}
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <RefreshCw
                className={`w-4 h-4 ${loading ? "animate-spin" : ""}`}
              />
              <span>Search</span>
            </button>
          </div>

          {/* Extended Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-white rounded-lg border border-gray-200">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={filters.startDate}
                  onChange={(e) =>
                    handleFilterChange("startDate", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={filters.endDate}
                  onChange={(e) =>
                    handleFilterChange("endDate", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Created By
                </label>
                <input
                  type="text"
                  placeholder="Creator username"
                  value={filters.createdBy}
                  onChange={(e) =>
                    handleFilterChange("createdBy", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sort By
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange("sortBy", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="create_dt">Created Date</option>
                  <option value="trx_no">Transaction Number</option>
                  <option value="sum_amount">Sum Amount</option>
                  <option value="net_amount">Net Amount</option>
                  <option value="approve_dt">Approval Date</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sort Order
                </label>
                <select
                  value={filters.sortOrder}
                  onChange={(e) =>
                    handleFilterChange(
                      "sortOrder",
                      e.target.value as "ASC" | "DESC"
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="DESC">Newest First</option>
                  <option value="ASC">Oldest First</option>
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={handleClearFilters}
                  className="w-full px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Results Count */}
        <div className="px-6 py-3 bg-gray-100 border-b border-gray-200">
          <p className="text-sm text-gray-600">
            Showing {transactions.length} of {pagination.totalRecords} results
            {pagination.totalRecords > 0 && (
              <span>
                {" "}
                (Page {pagination.currentPage} of {pagination.totalPages})
              </span>
            )}
          </p>
        </div>

        {/* Transaction List */}
        <div className="flex-1 overflow-auto" style={{ maxHeight: "50vh" }}>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">
                Loading transactions...
              </span>
            </div>
          ) : transactions.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  No transaction payment forms found
                </p>
                <p className="text-gray-400 text-sm">
                  Try adjusting your search criteria
                </p>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transaction No.
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sum Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      VAT Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Withhold Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      MDR Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Net Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      BBL Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Other Banks
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Approval Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created By
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Updated By
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Updated Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transactions.map((transaction) => (
                    <tr
                      key={transaction.id}
                      className={`hover:bg-gray-50 cursor-pointer ${
                        selectedTransaction?.id === transaction.id
                          ? "bg-blue-50"
                          : ""
                      }`}
                      onClick={() => handleSelectTransaction(transaction)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-900">
                          {transaction.trx_no}
                        </div>
                        {/* <div className="text-xs text-gray-500">ID: {transaction.id}</div> */}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(transaction.sum_amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-orange-600">
                          {formatCurrency(transaction.vat_amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-red-600">
                          {formatCurrency(transaction.withhold_amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-indigo-600">
                          {formatCurrency(transaction.mdr_amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-green-600">
                          {formatCurrency(transaction.net_amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-blue-600">
                          {formatCurrency(transaction.bank_bbl_amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-purple-600">
                          {formatCurrency(transaction.bank_oth_amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {transaction.approve_dt
                            ? formatDate(transaction.approve_dt)
                            : "-"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <User className="w-4 h-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-900">
                            {transaction.create_by}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDate(transaction.create_dt)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <User className="w-4 h-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-600">
                            {transaction.update_by || "-"}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600">
                          {transaction.update_dt
                            ? formatDate(transaction.update_dt)
                            : "-"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              loadTransactionDetails(transaction.trx_no);
                            }}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1"
                          >
                            <Eye className="w-4 h-4" />
                            <span>View</span>
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              generatePaymentRequestPDF(transaction.trx_no);
                            }}
                            disabled={loading}
                            className="text-green-600 hover:text-green-800 text-sm font-medium flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Eye className="w-4 h-4" />
                            <span>Preview PDF</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing page {pagination.currentPage} of {pagination.totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={!pagination.hasPreviousPage}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                >
                  Previous
                </button>
                <button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100"
          >
            Close
          </button>
          {selectedTransaction && onSelectTransaction && (
            <button
              onClick={() => {
                onSelectTransaction(selectedTransaction);
                onClose();
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Select Transaction
            </button>
          )}
        </div>
      </div>

      {/* Transaction Details Modal */}
      {showDetails && detailsData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl mx-4 max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-green-600 to-green-700 text-white">
              <h3 className="text-xl font-semibold">
                Transaction Details: {detailsData.paymentForm.trx_no}
              </h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    generatePaymentRequestPDF(detailsData.paymentForm.trx_no);
                  }}
                  disabled={loading}
                  className="p-2 hover:bg-green-800 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                  title="Preview Payment Request PDF"
                >
                  <Eye className="w-5 h-5" />
                  <span className="text-sm">Preview PDF</span>
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    generateTransactionSummaryReport(detailsData.paymentForm.trx_no);
                  }}
                  disabled={loading || generatingReport}
                  className="p-2 hover:bg-green-800 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                  title="Generate Transaction Summary Report"
                >
                  <BarChart3 className="w-5 h-5" />
                  <span className="text-sm">Summary Report</span>
                </button>
                <button
                  onClick={() => setShowDetails(false)}
                  className="p-2 hover:bg-green-800 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 overflow-auto" style={{ maxHeight: "70vh" }}>
              {/* Payment Form Summary */}
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-3">
                  Payment Form Summary
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 bg-gray-50 p-4 rounded-lg">
                  <div>
                    <p className="text-sm text-gray-600">Transaction No.</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {detailsData.paymentForm.trx_no}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Sum Amount</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(detailsData.paymentForm.sum_amount)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">VAT Amount</p>
                    <p className="text-lg font-semibold text-orange-600">
                      {formatCurrency(detailsData.paymentForm.vat_amount)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Withhold Amount</p>
                    <p className="text-lg font-semibold text-red-600">
                      {formatCurrency(detailsData.paymentForm.withhold_amount)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">MDR Amount</p>
                    <p className="text-lg font-semibold text-indigo-600">
                      {formatCurrency(detailsData.paymentForm.mdr_amount)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Net Amount</p>
                    <p className="text-lg font-semibold text-green-600">
                      {formatCurrency(detailsData.paymentForm.net_amount)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">BBL Bank Amount</p>
                    <p className="text-lg font-semibold text-blue-600">
                      {formatCurrency(detailsData.paymentForm.bank_bbl_amount)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Other Banks Amount</p>
                    <p className="text-lg font-semibold text-purple-600">
                      {formatCurrency(detailsData.paymentForm.bank_oth_amount)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Approval Date</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {detailsData.paymentForm.approve_dt
                        ? formatDate(detailsData.paymentForm.approve_dt)
                        : "Not Approved"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Created By</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {detailsData.paymentForm.create_by}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Created Date</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatDate(detailsData.paymentForm.create_dt)}
                    </p>
                  </div>
                  {/* <div>
                    <p className="text-sm text-gray-600">Updated By</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {detailsData.paymentForm.update_by || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Updated Date</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {detailsData.paymentForm.update_dt ? formatDate(detailsData.paymentForm.update_dt) : 'N/A'}
                    </p>
                  </div> */}
                </div>
              </div>

              {/* Related Transactions */}
              <div>
                <h4 className="text-lg font-semibold text-gray-800 mb-3">
                  Related Transactions ({detailsData.transactionDetails.length})
                </h4>
                <div className="overflow-x-auto">
                  <table className="w-full border border-gray-200 rounded-lg">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Merchant
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Channel
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Total Amount
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Bank
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Date
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {detailsData.transactionDetails.map((detail: any) => (
                        <tr key={detail.id} className="hover:bg-gray-50">
                          <td className="px-4 py-3">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {detail.merchant_name}
                              </div>
                              <div className="text-xs text-gray-500">
                                {detail.merchant_vat}
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900">
                            {detail.channel_type}
                          </td>
                          <td className="px-4 py-3 text-sm font-medium text-gray-900">
                            {formatCurrency(detail.total_amount)}
                          </td>
                          <td className="px-4 py-3">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                detail.bank_ref === "BBL"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-purple-100 text-purple-800"
                              }`}
                            >
                              {detail.bank_ref || "N/A"}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900">
                            {formatDate(detail.transaction_date)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Transaction Summary Report Modal */}
      <TransactionSummaryReportViewer
        isOpen={showTransactionSummaryReport}
        onClose={() => {
          setShowTransactionSummaryReport(false);
          clearReport();
        }}
        reportData={transactionSummaryReportData}
        isLoading={generatingReport}
        title="Transaction Summary Report"
      />
    </div>
  );
};

export default TransactionLogSearchModal;
