import { executeQuery } from '../db';

interface MerchantSummaryData {
  merchantVat: string;
  merchantName: string;
  transactionDate: string;
  channelType: string;
  transactionCount: number;
  totalAmount: number;
  mdrRate: number;
  mdrAmount: number;
  vatPercentage: number;
  vatAmount: number;
  netAmount: number;
  withholdingTaxRate: number;
  withholdTax: number;
  transferFee: number;
  reimbursementFee: number;
  serviceFee: number;
  finalNetAmount: number;
  cupBusinessTaxFee: number;
  tradeStatus: 'SUCCESS' | 'REFUND' | 'ADJUST'; // New field to identify row type
  bankCode: string; // Bank code for grouping transfers
  bankNameTh: string; // Thai bank name
  bankNameEn: string; // English bank name
  isTransfer: number; // 0 = not yet transferred, 1 = transfer already
  bankRef: string; //bank reference from tmst_bank table
}

// Removed unused GrandTotalsData interface

export class TransactionSummaryReportService {

  /**
   * Insert transaction summary details directly into transaction_summary_report_detail table
   * This method provides a direct way to insert summary data without generating a full report
   */
  async insertTransactionSummaryDetails(
    summaryDetails: MerchantSummaryData[],
    batchId: string,
    createdBy: string = 'SYSTEM'
  ): Promise<{ success: boolean; insertedCount: number; error?: string }> {
    try {
      console.log(`💾 Inserting ${summaryDetails.length} transaction summary details for batch: ${batchId}`);

      let insertedCount = 0;

      // Start transaction
      await executeQuery('BEGIN');

      try {
        // Insert each summary detail record
        for (const merchant of summaryDetails) {
          await executeQuery(`
            INSERT INTO transaction_summary_report_detail (
              merchant_vat, merchant_name, transaction_date, channel_type,
              transaction_count, total_amount, mdr_rate, mdr_amount, vat_percentage,
              vat_amount, net_amount, withholding_tax_rate, withhold_tax, transfer_fee,
              reimbursement_fee, service_fee, final_net_amount, cup_business_tax_fee,
              is_transfer, create_by, update_by, trade_status
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
            )
          `, [
            merchant.merchantVat, merchant.merchantName, merchant.transactionDate,
            merchant.channelType, merchant.transactionCount, merchant.totalAmount,
            merchant.mdrRate, merchant.mdrAmount, merchant.vatPercentage, merchant.vatAmount,
            merchant.netAmount, merchant.withholdingTaxRate, merchant.withholdTax,
            merchant.transferFee, merchant.reimbursementFee, merchant.serviceFee,
            merchant.finalNetAmount, merchant.cupBusinessTaxFee, merchant.isTransfer,
            createdBy, createdBy , merchant.tradeStatus
          ]);
          insertedCount++;
        }

        // Commit transaction
        await executeQuery('COMMIT');

        console.log(`✅ Successfully inserted ${insertedCount} transaction summary details`);

        return {
          success: true,
          insertedCount
        };

      } catch (error) {
        // Rollback on error
        await executeQuery('ROLLBACK');
        throw error;
      }

    } catch (error) {
      console.error('❌ Error inserting transaction summary details:', error);
      return {
        success: false,
        insertedCount: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create sample transaction summary data for testing
   */
  createSampleTransactionSummaryData(
    merchantVat: string = 'TEST123456789',
    merchantName: string = 'Test Merchant Co., Ltd.',
    transactionCount: number = 10,
    totalAmount: number = 1000.00
  ): MerchantSummaryData {
    const mdrRate = 2.5;
    const vatPercentage = 7.0;
    const withholdingTaxRate = 3.0;
    const transferFee = 25.0;

    const mdrAmount = this.roundToTwoDecimals((totalAmount * mdrRate) / 100);
    const vatAmount = this.roundToTwoDecimals((mdrAmount * vatPercentage) / 100);
    const netAmount = this.roundToTwoDecimals(totalAmount - mdrAmount);
    const withholdTax = this.roundToTwoDecimals((totalAmount * withholdingTaxRate) / 100);
    const finalNetAmount = this.roundToTwoDecimals(netAmount - vatAmount - withholdTax - transferFee);

    return {
      merchantVat,
      merchantName,
      transactionDate: this.formatDate(new Date()),
      channelType: 'WeChat',
      transactionCount,
      totalAmount,
      mdrRate,
      mdrAmount,
      vatPercentage,
      vatAmount,
      netAmount,
      withholdingTaxRate,
      withholdTax,
      transferFee,
      reimbursementFee: 0,
      serviceFee: 0,
      finalNetAmount,
      cupBusinessTaxFee: 0,
      isTransfer: 0,
      tradeStatus: 'SUCCESS',
      bankCode: 'BBL',
      bankNameTh: 'ธนาคารกรุงเทพ',
      bankNameEn: 'Bangkok Bank',
      bankRef: 'BBL'
    };
  }



  /**
   * Enhanced workflow: Generate summary report details directly from uploaded transactions
   * This method groups transactions by merchant_id and creates summary records
   */
  async generateSummaryFromUploadedTransactions(
    batchId: string,
    processedFiles: string[],
    createdBy: string
  ): Promise<{ success: boolean; summaryDetails?: any[]; error?: string }> {
    try {
      console.log(`📊 Enhanced workflow: Generating transaction summary from uploaded transactions for batch: ${batchId}`);

      // Step 1: Check which tables exist in the database
      const tableCheckResult = await executeQuery(`
        SELECT
          EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant') as merchant_exists,
          EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant_wechat') as merchant_wechat_exists,
          EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'network_service') as network_service_exists,
          EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant_bank') as merchant_bank_exists,
          EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tmst_bank') as tmst_bank_exists
      `);

      const tableExists = tableCheckResult.rows[0];
      console.log(`📊 Table availability check:`, tableExists);

      // Step 2: Build dynamic query based on available tables
      let selectFields = ['t.*'];
      let joinClauses = [];

      // Add merchant fields if table exists
      if (tableExists.merchant_exists) {
        selectFields.push('m.merchant_id', 'm.merchant_name', 'm.withholding_tax', 'm.transfer_fee');
        joinClauses.push('LEFT JOIN merchant m ON t.transaction_merchant_vat = m.merchant_vat');

        // Add bank information via merchant_bank and tmst_bank if tables exist
        if (tableExists.merchant_bank_exists && tableExists.tmst_bank_exists) {
          selectFields.push('b.bank_code', 'b.bank_name_th', 'b.bank_name_en','b.bank_ref');
          joinClauses.push('LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true');
          joinClauses.push('LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true');
        } else {
          selectFields.push('NULL as bank_code', 'NULL as bank_name_th', 'NULL as bank_name_en', 'NULL as bank_ref');
        }
      } else {
        selectFields.push('NULL as merchant_id', 'NULL as merchant_name', '3.0 as withholding_tax', '0 as transfer_fee');
        selectFields.push('NULL as bank_code', 'NULL as bank_name_th', 'NULL as bank_name_en', 'NULL as bank_ref');
      }

      // Add merchant_wechat fields if table exists
      if (tableExists.merchant_wechat_exists && tableExists.merchant_exists) {
        selectFields.push('mw.wechat_rate');
        joinClauses.push('LEFT JOIN merchant_wechat mw ON m.merchant_id = mw.merchant_id');
      } else {
        selectFields.push('2.5 as wechat_rate');
      }

      // Add network_service fields if table exists
      if (tableExists.network_service_exists) {
        selectFields.push('ns.vat_percentage as vat_tax_percent');
        joinClauses.push('LEFT JOIN network_service ns ON 1=1');
      } else {
        selectFields.push('7.0 as vat_tax_percent');
      }

      // Build the complete query
      const query = `
        SELECT ${selectFields.join(', ')}
        FROM transaction_e_pos t
        ${joinClauses.join(' ')}
        WHERE t.transaction_file_name = ANY($1)
        AND t.transaction_trade_status IN ('success', 'refund')
        ORDER BY t.transaction_merchant_id, t.transaction_time`;

      console.log(`📊 Executing enhanced query with available tables:`, {
        merchant_exists: tableExists.merchant_exists,
        merchant_wechat_exists: tableExists.merchant_wechat_exists,
        network_service_exists: tableExists.network_service_exists,
        merchant_bank_exists: tableExists.merchant_bank_exists,
        tmst_bank_exists: tableExists.tmst_bank_exists
      });

      console.log(`🔍 Bank tables status:`, {
        merchant_bank_exists: tableExists.merchant_bank_exists,
        tmst_bank_exists: tableExists.tmst_bank_exists,
        will_include_bank_joins: tableExists.merchant_bank_exists && tableExists.tmst_bank_exists
      });

      console.log(`🔍 Enhanced query being executed:`, query);
      console.log(`🔍 Query parameters:`, [processedFiles]);

      const transactionsResult = await executeQuery(query, [processedFiles]);

      const transactions = transactionsResult.rows;
      console.log(`📊 Found ${transactions.length} successful transactions to process`);

      if (transactions.length > 0) {
        const firstRow = transactions[0];
        console.log("firstRow",firstRow)
        console.log(`🔍 First row bank data:`, {
          bank_code: firstRow.bank_code,
          bank_name_th: firstRow.bank_name_th,
          bank_name_en: firstRow.bank_name_en,
          merchant_id: firstRow.merchant_id,
          merchant_name: firstRow.merchant_name,
          bank_ref: firstRow.bank_ref,
        });
      }

      if (transactions.length === 0) {
        return {
          success: false,
          error: 'No successful transactions found for summary generation'
        };
      }

      // Step 2: Group transactions by merchant_id
      const merchantGroups = new Map<string, any[]>();
      for (const transaction of transactions) {
        const merchantId = transaction.transaction_merchant_id || transaction.merchant_id || 'UNKNOWN';
        if (!merchantGroups.has(merchantId)) {
          merchantGroups.set(merchantId, []);
        }
        merchantGroups.get(merchantId)!.push(transaction);
      }

      console.log(`📊 Grouped transactions into ${merchantGroups.size} merchant groups`);

      // Step 3: Generate summary data for each merchant group
      const summaryDetails = await this.generateMerchantSummaries(merchantGroups, batchId, createdBy);

      // Step 4: Save to database (detail records only)
      const insertedCount = await this.saveSummaryDetailsToDatabase(
        summaryDetails,
        createdBy
      );

      console.log(`✅ Enhanced workflow: Transaction summary generation completed`);
      console.log(`📊 Generated ${summaryDetails.length} merchant summary records`);
      console.log(`💾 Saved ${insertedCount} transaction summary detail records`);

      return {
        success: true,
        summaryDetails
      };

    } catch (error) {
      console.error('❌ Error in enhanced workflow for transaction summary:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate report data using the same calculation logic as the PDF report
   * @deprecated - This method is no longer used since we only insert into detail table
   */
  /*
  // DEPRECATED: This method is no longer used since we only insert into detail table
  private async generateReportData(
    batchId: string,
    processedFiles: string[],
    totalFiles: number,
    totalTransactions: number,
    createdBy: string
  ): Promise<TransactionSummaryReportData> {
    // ... method implementation commented out ...
  }
  */

  /**
   * Save summary details directly to database (detail table only)
   */
  private async saveSummaryDetailsToDatabase(
    summaryDetails: MerchantSummaryData[],
    createdBy: string
  ): Promise<number> {
    // Start transaction
    await executeQuery('BEGIN');

    try {
      let insertedCount = 0;
      console.log("summaryDetails_rith",summaryDetails)
      // Insert detail records directly (no main report record)
      for (const merchant of summaryDetails) {
        await executeQuery(`
          INSERT INTO transaction_summary_report_detail (
            merchant_vat, merchant_name, transaction_date, channel_type,
            transaction_count, total_amount, mdr_rate, mdr_amount, vat_percentage,
            vat_amount, net_amount, withholding_tax_rate, withhold_tax, transfer_fee,
            reimbursement_fee, service_fee, final_net_amount, cup_business_tax_fee,
            is_transfer, create_by, update_by , bank_ref , trade_status
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22 , $23
          )
        `, [
          merchant.merchantVat, merchant.merchantName, merchant.transactionDate,
          merchant.channelType, merchant.transactionCount, merchant.totalAmount,
          merchant.mdrRate, merchant.mdrAmount, merchant.vatPercentage, merchant.vatAmount,
          merchant.netAmount, merchant.withholdingTaxRate, merchant.withholdTax,
          merchant.transferFee, merchant.reimbursementFee, merchant.serviceFee,
          merchant.finalNetAmount, merchant.cupBusinessTaxFee, merchant.isTransfer,
          createdBy, createdBy, merchant.bankRef, merchant.tradeStatus
        ]);
        insertedCount++;
      }

      // Commit transaction
      await executeQuery('COMMIT');

      console.log(`💾 Successfully saved transaction summary details (detail records only)`);
      console.log(`📊 Inserted ${insertedCount} detail records into transaction_summary_report_detail`);

      return insertedCount; // Return count instead of report ID

    } catch (error) {
      // Rollback on error
      await executeQuery('ROLLBACK');
      console.error('❌ Error saving summary details to database:', error);
      throw error;
    }
  }



  /**
   * Generate merchant summaries from grouped transaction data with separate SUCCESS/REFUND rows
   */
  private async generateMerchantSummaries(
    merchantGroups: Map<string, any[]>,
    _batchId: string,
    _createdBy: string
  ): Promise<MerchantSummaryData[]> {
    const summaries: MerchantSummaryData[] = [];

    for (const [merchantId, merchantTransactions] of merchantGroups) {
      const firstTransaction = merchantTransactions[0];

      console.log(`🔍 Original workflow - Debugging firstTransaction for merchant ${merchantId}:`, {
        bank_code: firstTransaction.bank_code,
        bank_name_th: firstTransaction.bank_name_th,
        bank_name_en: firstTransaction.bank_name_en,
        transaction_bank_type: firstTransaction.transaction_bank_type,
        merchant_name: firstTransaction.merchant_name,
        transaction_merchant_name: firstTransaction.transaction_merchant_name,
        keys: Object.keys(firstTransaction).filter(key => key.includes('bank')),
        allKeys: Object.keys(firstTransaction).slice(0, 10) // Show first 10 keys
      });

      // Separate success and refund transactions
      const successTransactions = merchantTransactions.filter(t =>
        (t.transaction_trade_status || '').toLowerCase() === 'success'
      );
      const refundTransactions = merchantTransactions.filter(t =>
        (t.transaction_trade_status || '').toLowerCase() === 'refund'
      );

      // Get financial rates from database or use defaults
      const mdrRate = parseFloat(firstTransaction.wechat_rate) || 2.5; // Default 2.5%
      const vatPercentage = parseFloat(firstTransaction.vat_tax_percent) || 7.0; // Default 7%
      const withholdingTaxRate = parseFloat(firstTransaction.withholding_tax) || 3.0; // Default 3%
      const baseTransferFee = parseFloat(firstTransaction.transfer_fee) || 0;

      console.log(`📊 Enhanced financial rates for merchant ${merchantId}: MDR=${mdrRate}%, VAT=${vatPercentage}%, Withholding=${withholdingTaxRate}%, Transfer Fee=${baseTransferFee}`);

      // Create SUCCESS row if there are success transactions
      if (successTransactions.length > 0) {
        const successAmount = this.roundToTwoDecimals(
          successTransactions.reduce((sum, t) => sum + Math.abs(parseFloat(t.transaction_amount || 0)), 0)
        );

        const successMerchantSummary = this.createMerchantSummaryRow(
          firstTransaction.transaction_merchant_vat || 'UNKNOWN',
          firstTransaction,
          successTransactions.length,
          successAmount,
          mdrRate,
          vatPercentage,
          withholdingTaxRate,
          baseTransferFee,
          'SUCCESS'
        );

        summaries.push(successMerchantSummary);
        console.log(`📊 Generated SUCCESS summary for merchant ${merchantId}: ${successTransactions.length} transactions, ${this.formatCurrency(successAmount)} THB`);
      }

      // Create REFUND row if there are refund transactions
      if (refundTransactions.length > 0) {
        const refundAmount = this.roundToTwoDecimals(
          refundTransactions.reduce((sum, t) => sum + Math.abs(parseFloat(t.transaction_amount || 0)), 0)
        );

        const refundMerchantSummary = this.createMerchantSummaryRow(
          firstTransaction.transaction_merchant_vat || 'UNKNOWN',
          firstTransaction,
          refundTransactions.length,
          -refundAmount, // Negative amount for refunds
          mdrRate,
          vatPercentage,
          withholdingTaxRate,
          baseTransferFee,
          'REFUND'
        );

        summaries.push(refundMerchantSummary);
        console.log(`📊 Generated REFUND summary for merchant ${merchantId}: ${refundTransactions.length} transactions, ${this.formatCurrency(-refundAmount)} THB`);
      }
    }

    return summaries;
  }

  /**
   * Calculate bank subtotals from merchant summaries
   * @deprecated - This method is no longer used since we only insert into detail table
   */
  /*
  private calculateBankSubtotals(summaries: MerchantSummaryData[]): any[] {
    const bankGroups = new Map<string, MerchantSummaryData[]>();

    // Group summaries by bank code
    for (const summary of summaries) {
      const bankCode = summary.bankCode || 'UNKNOWN';
      if (!bankGroups.has(bankCode)) {
        bankGroups.set(bankCode, []);
      }
      bankGroups.get(bankCode)!.push(summary);
    }

    // Calculate subtotals for each bank
    const bankSubtotals = [];
    for (const [bankCode, bankSummaries] of bankGroups) {
      const subtotals = {
        totalTransactions: bankSummaries.reduce((sum, s) => sum + s.transactionCount, 0),
        totalAmount: this.roundToTwoDecimals(bankSummaries.reduce((sum, s) => sum + s.totalAmount, 0)),
        totalMdrAmount: this.roundToTwoDecimals(bankSummaries.reduce((sum, s) => sum + s.mdrAmount, 0)),
        totalTransferFee: this.roundToTwoDecimals(bankSummaries.reduce((sum, s) => sum + s.transferFee, 0)),
        totalVatAmount: this.roundToTwoDecimals(bankSummaries.reduce((sum, s) => sum + s.vatAmount, 0)),
        totalNetAmount: this.roundToTwoDecimals(bankSummaries.reduce((sum, s) => sum + s.netAmount, 0)),
        totalWithholdTax: this.roundToTwoDecimals(bankSummaries.reduce((sum, s) => sum + s.withholdTax, 0)),
        totalReimbursementFee: this.roundToTwoDecimals(bankSummaries.reduce((sum, s) => sum + s.reimbursementFee, 0)),
        totalServiceFee: this.roundToTwoDecimals(bankSummaries.reduce((sum, s) => sum + s.serviceFee, 0)),
        totalFinalNetAmount: this.roundToTwoDecimals(bankSummaries.reduce((sum, s) => sum + s.finalNetAmount, 0))
      };

      bankSubtotals.push({
        bankCode,
        bankNameTh: bankSummaries[0]?.bankNameTh || 'Unknown Bank',
        bankNameEn: bankSummaries[0]?.bankNameEn || 'Unknown Bank',
        subtotals
      });
    }

    return bankSubtotals;
  }
  */

  /**
   * Calculate grand totals from merchant summaries (now with separate SUCCESS/REFUND rows)
   * @deprecated - This method is no longer used since we only insert into detail table
   */
  /*
  private calculateGrandTotals(summaries: MerchantSummaryData[]): GrandTotalsData {
    const grandTotals: GrandTotalsData = {
      totalTransactions: 0,
      totalAmount: 0,
      totalMdrAmount: 0,
      totalVatAmount: 0,
      totalNetAmount: 0,
      totalWithholdTax: 0,
      totalTransferFee: 0,
      totalReimbursementFee: 0,
      totalServiceFee: 0,
      totalFinalNetAmount: 0,
      totalCupBusinessTaxFee: 0,
      averageMdrRate: 0,
      averageVatPercentage: 0,
      averageWithholdingTaxRate: 0
    };

    // Track totals for average calculations
    let totalMdrRateSum = 0;
    let totalVatPercentageSum = 0;
    let totalWithholdingTaxRateSum = 0;

    for (const summary of summaries) {
      grandTotals.totalTransactions += summary.transactionCount;
      grandTotals.totalAmount += summary.totalAmount;
      grandTotals.totalMdrAmount += summary.mdrAmount;
      grandTotals.totalVatAmount += summary.vatAmount;
      grandTotals.totalNetAmount += summary.netAmount;
      grandTotals.totalWithholdTax += summary.withholdTax;
      grandTotals.totalTransferFee += summary.transferFee;
      grandTotals.totalReimbursementFee += summary.reimbursementFee;
      grandTotals.totalServiceFee += summary.serviceFee;
      grandTotals.totalFinalNetAmount += summary.finalNetAmount;
      grandTotals.totalCupBusinessTaxFee += summary.cupBusinessTaxFee;

      // Track for averages
      totalMdrRateSum += summary.mdrRate;
      totalVatPercentageSum += summary.vatPercentage;
      totalWithholdingTaxRateSum += summary.withholdingTaxRate;
    }

    // Calculate averages
    const merchantCount = summaries.length;
    if (merchantCount > 0) {
      grandTotals.averageMdrRate = this.roundToTwoDecimals(totalMdrRateSum / merchantCount);
      grandTotals.averageVatPercentage = this.roundToTwoDecimals(totalVatPercentageSum / merchantCount);
      grandTotals.averageWithholdingTaxRate = this.roundToTwoDecimals(totalWithholdingTaxRateSum / merchantCount);
    }

    // Round all totals
    grandTotals.totalAmount = this.roundToTwoDecimals(grandTotals.totalAmount);
    grandTotals.totalMdrAmount = this.roundToTwoDecimals(grandTotals.totalMdrAmount);
    grandTotals.totalVatAmount = this.roundToTwoDecimals(grandTotals.totalVatAmount);
    grandTotals.totalNetAmount = this.roundToTwoDecimals(grandTotals.totalNetAmount);
    grandTotals.totalWithholdTax = this.roundToTwoDecimals(grandTotals.totalWithholdTax);
    grandTotals.totalTransferFee = this.roundToTwoDecimals(grandTotals.totalTransferFee);
    grandTotals.totalReimbursementFee = this.roundToTwoDecimals(grandTotals.totalReimbursementFee);
    grandTotals.totalServiceFee = this.roundToTwoDecimals(grandTotals.totalServiceFee);
    grandTotals.totalFinalNetAmount = this.roundToTwoDecimals(grandTotals.totalFinalNetAmount);
    grandTotals.totalCupBusinessTaxFee = this.roundToTwoDecimals(grandTotals.totalCupBusinessTaxFee);

    console.log(`📊 Grand totals calculated: ${grandTotals.totalTransactions} transactions, ${this.formatCurrency(grandTotals.totalAmount)} THB`);

    return grandTotals;
  }
  */

  /**
   * Format currency for logging
   */
  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('th-TH', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Utility methods
   */
  private roundToTwoDecimals(num: number): number {
    return Math.round(num * 100) / 100;
  }

  /**
   * Create a merchant summary row for either SUCCESS or REFUND transactions
   * Following the enhanced financial calculation methodology
   */
  private createMerchantSummaryRow(
    vatNumber: string,
    firstTransaction: any,
    transactionCount: number,
    totalAmount: number,
    mdrRate: number,
    vatPercentage: number,
    withholdingTaxRate: number,
    baseTransferFee: number,
    tradeStatus: 'SUCCESS' | 'REFUND'
  ): MerchantSummaryData {
    // For SUCCESS: positive amounts, for REFUND: negative amounts
    const isRefund = tradeStatus === 'REFUND';
    const signedAmount = isRefund ? -Math.abs(totalAmount) : Math.abs(totalAmount);
    
    console.log(`📊 Creating ${tradeStatus} row for merchant ${vatNumber}: Amount=${signedAmount}, Count=${transactionCount}`);

    // 1. MDR Amount = Transaction Amount × (MDR% ÷ 100)
    const mdrAmount = this.roundToTwoDecimals(signedAmount * (mdrRate / 100));
    
    // 2. Transfer Fee = Direct value (maintain sign for refunds)
    const transferFee = isRefund ? -Math.abs(baseTransferFee) : baseTransferFee;
    
    // 3. VAT Amount = (Transfer Fee × VAT% ÷ 100) + (MDR Amount × VAT% ÷ 100)
    const vatOnTransferFee = this.roundToTwoDecimals(transferFee * (vatPercentage / 100));
    const vatOnMdrAmount = this.roundToTwoDecimals(mdrAmount * (vatPercentage / 100));
    const vatAmount = this.roundToTwoDecimals(vatOnTransferFee + vatOnMdrAmount);
    
    // 4. Net Amount = Transaction Amount - (MDR Amount + Transfer Fee + VAT Amount)
    const netAmount = this.roundToTwoDecimals(signedAmount - (mdrAmount + transferFee + vatAmount));
    
    // 5. Withholding Tax = (MDR Amount × (Withholding Tax% ÷ 100)) + (Transfer Fee × (Withholding Tax% ÷ 100))
    const withholdTaxOnMdr = this.roundToTwoDecimals(mdrAmount * (withholdingTaxRate / 100));
    const withholdTaxOnTransferFee = this.roundToTwoDecimals(transferFee * (withholdingTaxRate / 100));
    const withholdTax = this.roundToTwoDecimals(withholdTaxOnMdr + withholdTaxOnTransferFee);
    
    // 6. Reimbursement Fee = Transaction Amount × 0.5%
    const reimbursementFee = this.roundToTwoDecimals(signedAmount * 0.005);
    
    // 7. Channel-specific fees based on transaction type
    const channelType = (firstTransaction?.transaction_channel_type || '').toLowerCase();
    const isWechat = channelType === 'wechat' || channelType.includes('wechat');
    
    // Service Fee = 0% for WeChat, 0.1% for other channels
    const serviceFee = isWechat ? 0 : this.roundToTwoDecimals(signedAmount * 0.001);
    
    // CUP Business Tax = 0% for WeChat, 0.05% for other channels  
    const cupBusinessTaxFee = isWechat ? 0 : this.roundToTwoDecimals(signedAmount * 0.0005);
    
    // 8. Final Net Amount = Transaction Amount - (Transfer Fee + Reimbursement Fee + Service Fee + CUP Business Tax)
    const finalNetAmount = this.roundToTwoDecimals(
      signedAmount - (transferFee + reimbursementFee + serviceFee + cupBusinessTaxFee)
    );

    console.log(`📊 ${tradeStatus} Financial Breakdown for ${vatNumber}:
      - Transaction Amount: ${this.formatCurrency(signedAmount)}
      - MDR Amount (${mdrRate}%): ${this.formatCurrency(mdrAmount)}
      - Transfer Fee: ${this.formatCurrency(transferFee)}
      - VAT Amount (${vatPercentage}%): ${this.formatCurrency(vatAmount)}
      - Net Amount: ${this.formatCurrency(netAmount)}
      - Withholding Tax (${withholdingTaxRate}%): ${this.formatCurrency(withholdTax)}
      - Reimbursement Fee (0.5%): ${this.formatCurrency(reimbursementFee)}
      - Service Fee (${isWechat ? '0%' : '0.1%'}): ${this.formatCurrency(serviceFee)}
      - CUP Business Tax (${isWechat ? '0%' : '0.05%'}): ${this.formatCurrency(cupBusinessTaxFee)}
      - Final Net Amount: ${this.formatCurrency(finalNetAmount)}`);

    return {
      merchantVat: vatNumber,
      merchantName: firstTransaction?.merchant_name || firstTransaction?.transaction_merchant_name || 'Unknown Merchant',
      transactionDate: this.formatDate(new Date(firstTransaction.transaction_time)),
      channelType: firstTransaction?.transaction_channel_type || 'Unknown',
      transactionCount,
      totalAmount: signedAmount,
      mdrRate,
      mdrAmount,
      vatPercentage,
      vatAmount,
      netAmount,
      withholdingTaxRate,
      withholdTax,
      transferFee,
      reimbursementFee,
      serviceFee,
      finalNetAmount,
      cupBusinessTaxFee,
      tradeStatus,
      bankCode: firstTransaction?.bank_code || this.mapTransactionBankTypeToCode(firstTransaction?.transaction_bank_type) || 'UNKNOWN',
      bankNameTh: firstTransaction?.bank_name_th || this.mapTransactionBankTypeToName(firstTransaction?.transaction_bank_type, 'th') || 'Unknown Bank',
      bankNameEn: firstTransaction?.bank_name_en || this.mapTransactionBankTypeToName(firstTransaction?.transaction_bank_type, 'en') || 'Unknown Bank',
      isTransfer: 0 ,// Default to not yet transferred
      bankRef: firstTransaction?.bank_ref || 'UNKNOWN'
    };
  }

  /**
   * Format date to YYYY-MM-DD string
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Map transaction_bank_type to bank code
   */
  private mapTransactionBankTypeToCode(bankType: string | null | undefined): string | null {
    if (!bankType) return null;

    // Common bank type mappings
    const bankTypeMap: { [key: string]: string } = {
      'CMB_CREDIT': 'CMB',
      'CMB_DEBIT': 'CMB',
      'ICBC_CREDIT': 'ICBC',
      'ICBC_DEBIT': 'ICBC',
      'CCB_CREDIT': 'CCB',
      'CCB_DEBIT': 'CCB',
      'ABC_CREDIT': 'ABC',
      'ABC_DEBIT': 'ABC',
      'SPDB_CREDIT': 'SPDB',
      'SPDB_DEBIT': 'SPDB'
    };

    return bankTypeMap[bankType] || bankType;
  }

  /**
   * Map transaction_bank_type to bank name
   */
  private mapTransactionBankTypeToName(bankType: string | null | undefined, language: 'th' | 'en'): string | null {
    if (!bankType) return null;

    const bankCode = this.mapTransactionBankTypeToCode(bankType);
    if (!bankCode) return null;

    // Common bank name mappings
    const bankNameMap: { [key: string]: { th: string, en: string } } = {
      'CMB': { th: 'ธนาคารพาณิชย์จีน', en: 'China Merchants Bank' },
      'ICBC': { th: 'ธนาคารอุตสาหกรรมและพาณิชย์จีน', en: 'Industrial and Commercial Bank of China' },
      'CCB': { th: 'ธนาคารก่อสร้างจีน', en: 'China Construction Bank' },
      'ABC': { th: 'ธนาคารเกษตรจีน', en: 'Agricultural Bank of China' },
      'SPDB': { th: 'ธนาคารพัฒนาเซี่ยงไฮ้ผู่ตง', en: 'Shanghai Pudong Development Bank' }
    };

    return bankNameMap[bankCode]?.[language] || `${bankCode} Bank`;
  }
}

