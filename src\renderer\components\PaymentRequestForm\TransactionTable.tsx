import React from "react";
import { View, Text } from "@react-pdf/renderer";
import { styles } from "./styles";
import { PaymentRequestData, TRANSACTION_ROWS } from "./constants";

interface TransactionTableProps {
  transactions?: PaymentRequestData["transactions"];
}

export const TransactionTable: React.FC<TransactionTableProps> = ({ transactions }) => (
  <View style={styles.table}>
    {/* Header Row */}
    <View style={styles.tableRow}>
      <View style={styles.tableColHeaderBlack}>
        <Text style={styles.tableCellHeaderWhite}>Account to be charge</Text>
      </View>
      <View style={styles.tableColHeaderBlack}>
        <Text style={styles.tableCellHeaderWhite}>Amount</Text>
      </View>
    </View>
    
    {/* Data Rows */}
    {TRANSACTION_ROWS.map((row, index) => (
      <View style={styles.tableRow} key={index}>
        <View style={styles.tableCol}>
          <Text style={styles.tableCell}>{row.label}</Text>
        </View>
        <View style={styles.tableColAmount}>
          <Text style={styles.tableCell}>Amount</Text>
          <Text style={styles.tableCellAmount}>
            {row.field ? transactions?.[row.field] : row.value}
          </Text>
        </View>
      </View>
    ))}
  </View>
);
