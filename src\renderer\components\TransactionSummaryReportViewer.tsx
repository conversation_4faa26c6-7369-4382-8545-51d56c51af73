import React, { useState } from 'react';
import { PDFViewer, pdf } from '@react-pdf/renderer';
import { TransactionSummaryReport } from './TransactionSummaryReport';
import { ReportSummary } from '../services/reportService';
import { X, Download, RefreshCw } from 'lucide-react';

interface TransactionSummaryReportViewerProps {
  isOpen: boolean;
  onClose: () => void;
  reportData: ReportSummary | null;
  isLoading?: boolean;
  title?: string;
}

export const TransactionSummaryReportViewer: React.FC<TransactionSummaryReportViewerProps> = ({
  isOpen,
  onClose,
  reportData,
  isLoading = false,
  title = "Transaction Summary Report"
}) => {
  const [downloading, setDownloading] = useState(false);

  // Handle download of transaction summary report
  const handleDownloadReport = async () => {
    if (!reportData) return;

    setDownloading(true);
    try {
      console.log('📥 Generating transaction summary report PDF for download...');
      const blob = await pdf(<TransactionSummaryReport reportData={reportData} />).toBlob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `transaction-summary-report-${reportData.reportDate.replace(/\//g, '-')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      console.log('✅ Transaction summary report PDF download initiated successfully');
    } catch (error) {
      console.error('❌ Error downloading transaction summary report:', error);
    } finally {
      setDownloading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-[95vw] mx-4 h-[95vh] overflow-hidden flex flex-col border border-gray-200">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 text-white shadow-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
              <span className="text-xl">📊</span>
            </div>
            <div>
              <h2 className="text-xl font-bold tracking-tight">{title}</h2>
              {reportData && (
                <div className="flex items-center space-x-4 mt-1">
                  <p className="text-sm text-blue-100">
                    <span className="font-medium">{reportData.merchants.length}</span> merchants
                  </p>
                  <span className="text-blue-300">•</span>
                  <p className="text-sm text-blue-100">
                    <span className="font-medium">{reportData.grandTotals.totalTransactions}</span> transactions
                  </p>
                  <span className="text-blue-300">•</span>
                  <p className="text-sm text-blue-100">
                    <span className="font-medium">₿{reportData.grandTotals.totalAmount.toLocaleString()}</span>
                  </p>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Download Button */}
            <button
              onClick={handleDownloadReport}
              disabled={downloading || isLoading || !reportData}
              className="px-4 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 text-sm font-medium border border-white border-opacity-30"
              title="Download PDF Report"
            >
              {downloading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Download className="w-4 h-4" />
              )}
              <span>{downloading ? 'Downloading...' : 'Download PDF'}</span>
            </button>
            
            {/* Close Button */}
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-all duration-200 group"
              title="Close Report"
            >
              <X className="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden bg-gray-50">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center p-8">
                <div className="relative">
                  <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-6 flex items-center justify-center">
                    <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
                  </div>
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Generating Report</h3>
                <p className="text-gray-600">Please wait while we prepare your Transaction Summary Report...</p>
                <div className="mt-4 w-48 h-1 bg-gray-200 rounded-full mx-auto overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          ) : reportData ? (
            <div className="h-full p-4">
              <div className="h-full bg-white rounded-lg shadow-inner border border-gray-200 overflow-hidden">
                <PDFViewer
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                    borderRadius: '8px'
                  }}
                  showToolbar={true}
                >
                  <TransactionSummaryReport reportData={reportData} />
                </PDFViewer>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center p-8">
                <div className="w-20 h-20 bg-gray-100 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <X className="w-10 h-10 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">No Report Data</h3>
                <p className="text-gray-600 mb-4">Unable to load the transaction summary report.</p>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-200"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TransactionSummaryReportViewer;
