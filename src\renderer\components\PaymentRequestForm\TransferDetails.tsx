import React from "react";
import { View, Text } from "@react-pdf/renderer";
import { styles } from "./styles";
import { PaymentRequestData } from "./constants";

interface TransferDetailsProps {
  fromAccount?: PaymentRequestData["fromAccount"];
}

export const TransferDetails: React.FC<TransferDetailsProps> = ({ fromAccount }) => (
  <View style={styles.marginBottom20}>
    <Text style={[styles.textSize11, styles.marginBottom6]}>Transfer from</Text>
    <View style={styles.transferDetailsContainer}>
      <View style={[styles.transferDetailRow]}>
        <Text style={[styles.textSize10, styles.width80]}>A/C Name</Text>
        <Text style={[styles.textSize10, styles.marginRight10]}>:</Text>
        <Text style={styles.textSize10}>{fromAccount?.name}</Text>
      </View>
      <View style={styles.transferDetailRow}>
        <Text style={[styles.textSize10, styles.width80]}>Type Of A/C</Text>
        <Text style={[styles.textSize10, styles.marginRight10]}>:</Text>
        <Text style={styles.textSize10}>{fromAccount?.type}</Text>
      </View>
      <View style={styles.transferDetailRow}>
        <Text style={[styles.textSize10, styles.width80]}>Deposit Bank</Text>
        <Text style={[styles.textSize10, styles.marginRight10]}>:</Text>
        <Text style={styles.textSize10}>{fromAccount?.bank}</Text>
      </View>
      <View style={styles.transferDetailRow}>
        <Text style={[styles.textSize10, styles.width80]}>Branch</Text>
        <Text style={[styles.textSize10, styles.marginRight10]}>:</Text>
        <Text style={styles.textSize10}>{fromAccount?.branch}</Text>
      </View>
      <View style={styles.transferDetailRow}>
        <Text style={[styles.textSize10, styles.width80]}>A/C No.</Text>
        <Text style={[styles.textSize10, styles.marginRight10]}>:</Text>
        <Text style={styles.textSize10}>{fromAccount?.accountNo}</Text>
      </View>
    </View>
  </View>
);
