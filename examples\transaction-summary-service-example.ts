/**
 * Example: Using TransactionSummaryReportService to insert transaction summary data
 * 
 * This example demonstrates how to use the TransactionSummaryReportService
 * to insert transaction summary data into the transaction_summary_report_detail table.
 */

import { TransactionSummaryReportService } from '../src/main/services/transactionSummaryReportService';

// Example usage of TransactionSummaryReportService
async function exampleUsage() {
  console.log('🚀 TransactionSummaryReportService Example');
  
  // Initialize the service
  const summaryReportService = new TransactionSummaryReportService();
  
  try {
    // Example 1: Create sample transaction summary data
    console.log('\n📊 Example 1: Creating sample transaction summary data');
    
    const sampleData1 = summaryReportService.createSampleTransactionSummaryData(
      'TEST123456789',
      'Test Merchant 1 Co., Ltd.',
      15,
      1500.00
    );
    
    const sampleData2 = summaryReportService.createSampleTransactionSummaryData(
      'TEST987654321',
      'Test Merchant 2 Co., Ltd.',
      8,
      800.00
    );
    
    console.log('Sample Data 1:', sampleData1);
    console.log('Sample Data 2:', sampleData2);
    
    // Example 2: Insert transaction summary details directly
    console.log('\n💾 Example 2: Inserting transaction summary details');
    
    const batchId = `EXAMPLE_BATCH_${Date.now()}`;
    const summaryDetails = [sampleData1, sampleData2];
    
    const insertResult = await summaryReportService.insertTransactionSummaryDetails(
      summaryDetails,
      batchId,
      'EXAMPLE_USER'
    );
    
    if (insertResult.success) {
      console.log(`✅ Successfully inserted ${insertResult.insertedCount} transaction summary records`);
      console.log(`📊 Batch ID: ${batchId}`);
    } else {
      console.error(`❌ Failed to insert transaction summary details: ${insertResult.error}`);
    }
    
    // Example 3: Generate and save transaction summary details from uploaded transactions
    console.log('\n📈 Example 3: Generate summary from uploaded transactions');

    // This would typically be called after processing transaction files
    const processedFiles = ['example_transactions_1.xlsx', 'example_transactions_2.xlsx'];

    const reportResult = await summaryReportService.generateSummaryFromUploadedTransactions(
      batchId,
      processedFiles,
      'EXAMPLE_USER'
    );

    if (reportResult.success) {
      console.log(`✅ Generated transaction summary details successfully`);
      console.log(`📊 Generated ${reportResult.summaryDetails?.length || 0} merchant summary records`);
    } else {
      console.error(`❌ Failed to generate transaction summary details: ${reportResult.error}`);
    }
    
  } catch (error) {
    console.error('❌ Error in example usage:', error);
  }
}

// Example of creating custom transaction summary data
function createCustomTransactionSummaryData() {
  console.log('\n🛠️ Example: Creating custom transaction summary data');
  
  const customSummaryData = {
    merchantVat: 'CUSTOM123456789',
    merchantName: 'Custom Merchant Co., Ltd.',
    transactionDate: '2025-07-23',
    channelType: 'WeChat',
    transactionCount: 25,
    totalAmount: 2500.00,
    mdrRate: 2.8,
    mdrAmount: 70.00,
    vatPercentage: 7.0,
    vatAmount: 4.90,
    netAmount: 2430.00,
    withholdingTaxRate: 3.0,
    withholdTax: 75.00,
    transferFee: 30.00,
    reimbursementFee: 0,
    serviceFee: 0,
    finalNetAmount: 2320.10,
    cupBusinessTaxFee: 0,
    isTransfer: 0,
    tradeStatus: 'SUCCESS' as const,
    bankCode: 'SCB',
    bankNameTh: 'ธนาคารไทยพาณิชย์',
    bankNameEn: 'Siam Commercial Bank'
  };
  
  console.log('Custom Summary Data:', customSummaryData);
  return customSummaryData;
}

// Example of batch processing multiple merchants
async function batchProcessingExample() {
  console.log('\n🔄 Example: Batch processing multiple merchants');
  
  const summaryReportService = new TransactionSummaryReportService();
  
  // Create multiple merchant summaries
  const merchants = [
    { vat: 'MERCH001', name: 'Restaurant A', count: 50, amount: 5000 },
    { vat: 'MERCH002', name: 'Shop B', count: 30, amount: 3000 },
    { vat: 'MERCH003', name: 'Service C', count: 20, amount: 2000 },
  ];
  
  const summaryDetails = merchants.map(merchant => 
    summaryReportService.createSampleTransactionSummaryData(
      merchant.vat,
      merchant.name,
      merchant.count,
      merchant.amount
    )
  );
  
  const batchId = `BATCH_PROCESSING_${Date.now()}`;
  
  try {
    const result = await summaryReportService.insertTransactionSummaryDetails(
      summaryDetails,
      batchId,
      'BATCH_USER'
    );
    
    if (result.success) {
      console.log(`✅ Batch processing completed: ${result.insertedCount} records inserted`);
    } else {
      console.error(`❌ Batch processing failed: ${result.error}`);
    }
  } catch (error) {
    console.error('❌ Error in batch processing:', error);
  }
}

// Export functions for use in other files
export {
  exampleUsage,
  createCustomTransactionSummaryData,
  batchProcessingExample
};

// Run examples if this file is executed directly
if (require.main === module) {
  console.log('🎯 Running TransactionSummaryReportService Examples...\n');
  
  exampleUsage()
    .then(() => createCustomTransactionSummaryData())
    .then(() => batchProcessingExample())
    .then(() => console.log('\n✅ All examples completed!'))
    .catch(error => console.error('\n❌ Example execution failed:', error));
}
